{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write", "generate-client": "openapi-ts"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@tanstack/react-query": "^5.89.0", "lucide-react": "^0.544.0", "monaco-editor": "^0.53.0", "next": "15.5.3", "react": "19.1.0", "react-dom": "19.1.0", "zustand": "^5.0.8"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@hey-api/openapi-ts": "^0.83.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}