import { useEffect, useRef, useState } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage?: number;
  fileSize: number;
  lineCount: number;
  isLargeFile: boolean;
}

interface UsePerformanceMonitorOptions {
  threshold?: number; // File size threshold in bytes (default: 1MB)
  enableMemoryMonitoring?: boolean;
  onPerformanceIssue?: (metrics: PerformanceMetrics) => void;
}

export function usePerformanceMonitor(
  content: string,
  options: UsePerformanceMonitorOptions = {}
) {
  const {
    threshold = 1024 * 1024, // 1MB
    enableMemoryMonitoring = false,
    onPerformanceIssue
  } = options;

  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isOptimized, setIsOptimized] = useState(false);
  const renderStartTime = useRef<number>(0);

  useEffect(() => {
    renderStartTime.current = performance.now();
  }, [content]);

  useEffect(() => {
    const calculateMetrics = () => {
      const renderTime = performance.now() - renderStartTime.current;
      const fileSize = new Blob([content]).size;
      const lineCount = content.split('\n').length;
      const isLargeFile = fileSize > threshold;

      let memoryUsage: number | undefined;
      if (enableMemoryMonitoring && 'memory' in performance) {
        memoryUsage = (performance as any).memory?.usedJSHeapSize;
      }

      const newMetrics: PerformanceMetrics = {
        renderTime,
        memoryUsage,
        fileSize,
        lineCount,
        isLargeFile,
      };

      setMetrics(newMetrics);

      // Check for performance issues
      if (renderTime > 1000 || isLargeFile) {
        onPerformanceIssue?.(newMetrics);
        setIsOptimized(false);
      } else {
        setIsOptimized(true);
      }
    };

    // Debounce the calculation
    const timeoutId = setTimeout(calculateMetrics, 100);
    return () => clearTimeout(timeoutId);
  }, [content, threshold, enableMemoryMonitoring, onPerformanceIssue]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getOptimizationSuggestions = (): string[] => {
    if (!metrics) return [];

    const suggestions: string[] = [];

    if (metrics.isLargeFile) {
      suggestions.push('Consider enabling virtual scrolling for large files');
      suggestions.push('Disable minimap for better performance');
      suggestions.push('Reduce syntax highlighting complexity');
    }

    if (metrics.renderTime > 500) {
      suggestions.push('File rendering is slow, consider lazy loading');
      suggestions.push('Disable unnecessary editor features');
    }

    if (metrics.lineCount > 10000) {
      suggestions.push('Very large file detected, consider pagination');
      suggestions.push('Disable line decorations for better performance');
    }

    return suggestions;
  };

  const getRecommendedEditorOptions = () => {
    if (!metrics) return {};

    const options: any = {};

    if (metrics.isLargeFile) {
      options.minimap = { enabled: false };
      options.renderWhitespace = 'none';
      options.renderControlCharacters = false;
      options.folding = false;
      options.wordWrap = 'off';
      options.smoothScrolling = false;
      options.cursorSmoothCaretAnimation = 'off';
    }

    if (metrics.lineCount > 5000) {
      options.lineNumbers = 'off';
      options.glyphMargin = false;
      options.lineDecorationsWidth = 0;
      options.lineNumbersMinChars = 0;
    }

    if (metrics.renderTime > 1000) {
      options.quickSuggestions = false;
      options.suggestOnTriggerCharacters = false;
      options.parameterHints = { enabled: false };
      options.hover = { enabled: false };
    }

    return options;
  };

  return {
    metrics,
    isOptimized,
    formatFileSize,
    getOptimizationSuggestions,
    getRecommendedEditorOptions,
  };
}

// Hook for monitoring editor performance in real-time
export function useEditorPerformance() {
  const [fps, setFps] = useState<number>(0);
  const [isSmooth, setIsSmooth] = useState(true);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const animationFrame = useRef<number>();

  useEffect(() => {
    const measureFPS = () => {
      frameCount.current++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime.current >= 1000) {
        const currentFps = Math.round((frameCount.current * 1000) / (currentTime - lastTime.current));
        setFps(currentFps);
        setIsSmooth(currentFps >= 30); // Consider 30+ FPS as smooth
        
        frameCount.current = 0;
        lastTime.current = currentTime;
      }
      
      animationFrame.current = requestAnimationFrame(measureFPS);
    };

    animationFrame.current = requestAnimationFrame(measureFPS);

    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
    };
  }, []);

  return { fps, isSmooth };
}

// Hook for lazy loading large files
export function useLazyFileLoading(content: string, chunkSize: number = 1000) {
  const [loadedLines, setLoadedLines] = useState<number>(chunkSize);
  const [isFullyLoaded, setIsFullyLoaded] = useState(false);
  
  const lines = content.split('\n');
  const totalLines = lines.length;
  
  useEffect(() => {
    setLoadedLines(chunkSize);
    setIsFullyLoaded(totalLines <= chunkSize);
  }, [content, chunkSize, totalLines]);

  const loadMoreLines = () => {
    const newLoadedLines = Math.min(loadedLines + chunkSize, totalLines);
    setLoadedLines(newLoadedLines);
    setIsFullyLoaded(newLoadedLines >= totalLines);
  };

  const getVisibleContent = () => {
    return lines.slice(0, loadedLines).join('\n');
  };

  const getRemainingLines = () => {
    return Math.max(0, totalLines - loadedLines);
  };

  return {
    loadedLines,
    totalLines,
    isFullyLoaded,
    loadMoreLines,
    getVisibleContent,
    getRemainingLines,
    progress: (loadedLines / totalLines) * 100,
  };
}
