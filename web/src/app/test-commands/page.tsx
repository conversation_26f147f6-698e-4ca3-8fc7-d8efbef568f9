'use client';

import { useState } from 'react';
import { MessageInput } from '@/components/chat/message-input';
import { CommandResultDisplay } from '@/components/commands/command-result-display';
import { apiService } from '@/lib/api-service';

export default function TestCommandsPage() {
  const [messages, setMessages] = useState<Array<{
    type: 'message' | 'command' | 'error';
    content: string;
    timestamp: Date;
    result?: any;
  }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [selectedAgent, setSelectedAgent] = useState<string>('general');
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4');

  const handleSendMessage = async (message: string) => {
    const hasFileReferences = message.includes('@');
    const messageType = hasFileReferences ? 'Message with file references' : 'Message';
    setMessages(prev => [...prev, {
      type: 'message',
      content: `${messageType}: ${message}`,
      timestamp: new Date()
    }]);
  };

  const handleExecuteCommand = async (command: string, args: string) => {
    setMessages(prev => [...prev, {
      type: 'command',
      content: `Executing command: /${command} ${args}`,
      timestamp: new Date()
    }]);

    try {
      // Get a session ID if we don't have one
      if (!sessionId) {
        const sessionsResult = await apiService.getSessions();
        if (sessionsResult.success && sessionsResult.data && sessionsResult.data.length > 0) {
          const firstSessionId = sessionsResult.data[0].id;
          setSessionId(firstSessionId);
          setMessages(prev => [...prev, {
            type: 'message',
            content: `Using session: ${firstSessionId}`,
            timestamp: new Date()
          }]);

          // Execute the command
          const result = await apiService.executeCommand(firstSessionId, {
            command,
            arguments: args
          });

          if (result.success) {
            setMessages(prev => [...prev, {
              type: 'command',
              content: `Command executed successfully`,
              timestamp: new Date(),
              result: result.data
            }]);
          } else {
            setMessages(prev => [...prev, {
              type: 'error',
              content: `Command failed: ${result.error}`,
              timestamp: new Date()
            }]);
          }
        } else {
          setMessages(prev => [...prev, {
            type: 'error',
            content: `Failed to get sessions: ${sessionsResult.error}`,
            timestamp: new Date()
          }]);
        }
      } else {
        // Execute the command with existing session
        const result = await apiService.executeCommand(sessionId, {
          command,
          arguments: args
        });

        if (result.success) {
          setMessages(prev => [...prev, {
            type: 'command',
            content: `Command executed successfully`,
            timestamp: new Date(),
            result: result.data
          }]);
        } else {
          setMessages(prev => [...prev, {
            type: 'error',
            content: `Command failed: ${result.error}`,
            timestamp: new Date()
          }]);
        }
      }
    } catch (error) {
      setMessages(prev => [...prev, {
        type: 'error',
        content: `Command error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      }]);
    }
  };

  const handleStop = () => {
    setIsLoading(false);
  };

  const handleAgentSelect = (agentName: string) => {
    setSelectedAgent(agentName);
    setMessages(prev => [...prev, {
      type: 'message',
      content: `Selected agent: ${agentName}`,
      timestamp: new Date()
    }]);
  };

  const handleModelSelect = (providerId: string, modelId: string) => {
    setSelectedModel(`${providerId}/${modelId}`);
    setMessages(prev => [...prev, {
      type: 'message',
      content: `Selected model: ${providerId}/${modelId}`,
      timestamp: new Date()
    }]);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          Command Input Test
        </h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Command Testing
          </h2>
          <div className="text-gray-700 dark:text-gray-300 space-y-2 mb-4">
            <p><strong>Current session:</strong> {sessionId || 'None'}</p>
            <p>• Type <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/clear</code> to test the clear command</p>
            <p>• Type <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/help</code> to test the help command</p>
            <p>• Type <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/status</code> to test the status command</p>
          </div>
          <div className="flex gap-2 mb-4">
            <button
              onClick={() => handleExecuteCommand('clear', '')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Test /clear
            </button>
            <button
              onClick={() => handleExecuteCommand('help', '')}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Test /help
            </button>
            <button
              onClick={() => handleExecuteCommand('agents', '')}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              Test /agents
            </button>
            <button
              onClick={() => handleExecuteCommand('models', '')}
              className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
            >
              Test /models
            </button>
            <button
              onClick={() => setMessages([])}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Clear Log
            </button>
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Selected Agent:</strong> {selectedAgent}</p>
            <p><strong>Selected Model:</strong> {selectedModel}</p>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-6">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Messages & Commands
            </h3>
          </div>
          <div className="p-4 max-h-64 overflow-y-auto">
            {messages.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No messages yet. Try typing a message or command below.
              </p>
            ) : (
              <div className="space-y-4">
                {messages.map((msg, index) => (
                  <div key={index}>
                    <div
                      className={`p-3 rounded-lg ${
                        msg.type === 'command'
                          ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                          : msg.type === 'error'
                          ? 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                          : 'bg-gray-50 dark:bg-gray-700'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className={`text-xs font-medium uppercase tracking-wide ${
                          msg.type === 'command' ? 'text-blue-600 dark:text-blue-400' :
                          msg.type === 'error' ? 'text-red-600 dark:text-red-400' :
                          'text-gray-600 dark:text-gray-400'
                        }`}>
                          {msg.type}
                        </span>
                        <span className="text-xs text-gray-500">
                          {msg.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="text-sm text-gray-900 dark:text-white mb-2">
                        {msg.content}
                      </div>
                      {msg.result && (
                        <div className="mt-3 border-t pt-3">
                          <CommandResultDisplay
                            result={msg.result}
                            onAgentSelect={handleAgentSelect}
                            onModelSelect={handleModelSelect}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
          <MessageInput
            onSendMessage={handleSendMessage}
            onExecuteCommand={handleExecuteCommand}
            isLoading={isLoading}
            onStop={handleStop}
            disabled={false}
            placeholder="Type a message or use / for commands..."
            sessionId="test-session"
          />
        </div>
      </div>
    </div>
  );
}
