'use client';

import React, { useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import Monaco Editor to avoid SSR issues
const Editor = dynamic(() => import('@monaco-editor/react').then(mod => ({ default: mod.Editor })), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-full">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
});

export default function SimpleEditorDemo() {
  const [code, setCode] = useState(`// Welcome to the Enhanced Code Editor!
// This is a demonstration of Monaco Editor integration

function fibonacci(n: number): number {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

// Example usage
const result = fibonacci(10);
console.log(\`Fibonacci(10) = \${result}\`);

// Features demonstrated:
// ✨ Syntax highlighting for TypeScript
// 🧠 IntelliSense and auto-completion
// 🔍 Find and replace (Ctrl+F, Ctrl+H)
// 📁 Code folding and minimap
// ⌨️ VS Code keyboard shortcuts
// 🎨 Multiple themes

class Calculator {
  private history: number[] = [];
  
  add(a: number, b: number): number {
    const result = a + b;
    this.history.push(result);
    return result;
  }
  
  getHistory(): number[] {
    return [...this.history];
  }
}

const calc = new Calculator();
calc.add(5, 3);
calc.add(10, 20);
console.log(calc.getHistory());
`);

  const [theme, setTheme] = useState<'vs-dark' | 'vs-light' | 'hc-black'>('vs-dark');
  const [fontSize, setFontSize] = useState(14);
  const [showMinimap, setShowMinimap] = useState(true);

  const handleEditorChange = (value: string | undefined) => {
    setCode(value || '');
  };

  const cycleTheme = () => {
    const themes = ['vs-dark', 'vs-light', 'hc-black'] as const;
    const currentIndex = themes.indexOf(theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setTheme(nextTheme);
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Simple Monaco Editor Demo
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            A basic demonstration of Monaco Editor with TypeScript syntax highlighting and IntelliSense.
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          {/* Toolbar */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Code Editor
              </h2>
              
              <div className="flex items-center space-x-4">
                {/* Font Size Controls */}
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600 dark:text-gray-300">Font Size:</label>
                  <button
                    onClick={() => setFontSize(Math.max(10, fontSize - 2))}
                    className="px-2 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500"
                  >
                    -
                  </button>
                  <span className="text-sm text-gray-600 dark:text-gray-300 min-w-[3rem] text-center">
                    {fontSize}px
                  </span>
                  <button
                    onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                    className="px-2 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500"
                  >
                    +
                  </button>
                </div>

                {/* Theme Toggle */}
                <button
                  onClick={cycleTheme}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  Theme: {theme}
                </button>

                {/* Minimap Toggle */}
                <button
                  onClick={() => setShowMinimap(!showMinimap)}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    showMinimap
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                  }`}
                >
                  Minimap: {showMinimap ? 'On' : 'Off'}
                </button>
              </div>
            </div>
          </div>

          {/* Editor */}
          <div className="h-[600px]">
            <Editor
              height="100%"
              language="typescript"
              value={code}
              theme={theme}
              onChange={handleEditorChange}
              options={{
                fontSize: fontSize,
                minimap: { enabled: showMinimap },
                automaticLayout: true,
                scrollBeyondLastLine: false,
                renderWhitespace: 'selection',
                renderControlCharacters: true,
                folding: true,
                foldingStrategy: 'indentation',
                showFoldingControls: 'always',
                matchBrackets: 'always',
                bracketPairColorization: { enabled: true },
                guides: {
                  bracketPairs: true,
                  indentation: true,
                },
                suggestOnTriggerCharacters: true,
                acceptSuggestionOnEnter: 'on',
                tabCompletion: 'on',
                wordBasedSuggestions: 'allDocuments',
                quickSuggestions: {
                  other: true,
                  comments: true,
                  strings: true,
                },
                parameterHints: { enabled: true },
                codeLens: true,
                lightbulb: { enabled: true },
                hover: { enabled: true },
                links: true,
                colorDecorators: true,
                formatOnPaste: true,
                formatOnType: true,
                autoIndent: 'full',
                detectIndentation: true,
                insertSpaces: true,
                tabSize: 2,
              }}
            />
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
              <div>
                Lines: {code.split('\n').length} | Characters: {code.length}
              </div>
              <div>
                Language: TypeScript | Theme: {theme} | Font: {fontSize}px
              </div>
            </div>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
              🎨 Editor Features
            </h3>
            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-2">
              <li>• Syntax highlighting</li>
              <li>• IntelliSense auto-completion</li>
              <li>• Error detection and warnings</li>
              <li>• Code folding and minimap</li>
              <li>• Multiple themes</li>
              <li>• Customizable font size</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
              ⌨️ Keyboard Shortcuts
            </h3>
            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-2">
              <li>• <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Ctrl+F</kbd> Find</li>
              <li>• <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Ctrl+H</kbd> Replace</li>
              <li>• <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Ctrl+G</kbd> Go to line</li>
              <li>• <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Ctrl+D</kbd> Select next</li>
              <li>• <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Ctrl+/</kbd> Comment</li>
              <li>• <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Alt+↑/↓</kbd> Move lines</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
              🚀 Performance
            </h3>
            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-2">
              <li>• Fast rendering</li>
              <li>• Efficient memory usage</li>
              <li>• Smooth scrolling</li>
              <li>• Responsive design</li>
              <li>• No SSR issues</li>
              <li>• Dynamic loading</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">
            💡 Try These Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800 dark:text-blue-200">
            <div>
              <h4 className="font-medium mb-2">IntelliSense:</h4>
              <ul className="space-y-1">
                <li>• Type "calc." to see auto-completion</li>
                <li>• Hover over functions for documentation</li>
                <li>• See error squiggles for syntax issues</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Navigation:</h4>
              <ul className="space-y-1">
                <li>• Press Ctrl+G and go to line 15</li>
                <li>• Use Ctrl+F to search for "fibonacci"</li>
                <li>• Try folding the Calculator class</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
