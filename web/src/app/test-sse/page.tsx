'use client';

import { useState, useEffect } from 'react';
import { getSSEService, cleanupSSEService } from '@/lib/sse-service';
import type { Event } from '@/lib/api-client/types.gen';

export default function TestSSEPage() {
  const [events, setEvents] = useState<Event[]>([]);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const sseService = getSSEService({
      onConnected: () => {
        console.log('SSE connected');
        setConnected(true);
        setError(null);
      },
      onDisconnected: () => {
        console.log('SSE disconnected');
        setConnected(false);
      },
      onError: (err) => {
        console.error('SSE error:', err);
        setError(err instanceof Error ? err.message : 'SSE connection error');
        setConnected(false);
      },
      onMessageUpdated: (event) => {
        console.log('Message updated:', event);
        setEvents(prev => [...prev, event]);
      },
      onMessagePartUpdated: (event) => {
        console.log('Message part updated:', event);
        setEvents(prev => [...prev, event]);
      },
      onMessageRemoved: (event) => {
        console.log('Message removed:', event);
        setEvents(prev => [...prev, event]);
      }
    });

    // Connect to SSE if not already connected
    if (!sseService.getConnectionStatus()) {
      sseService.connect().catch(console.error);
    }

    // Cleanup on unmount
    return () => {
      cleanupSSEService();
    };
  }, []);

  const clearEvents = () => {
    setEvents([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          SSE Connection Test
        </h1>

        {/* Connection Status */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Connection Status
          </h2>
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-gray-700 dark:text-gray-300">
              {connected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          {error && (
            <div className="mt-3 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded text-red-700 dark:text-red-300">
              Error: {error}
            </div>
          )}
        </div>

        {/* Events */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Events ({events.length})
            </h2>
            <button
              onClick={clearEvents}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Clear Events
            </button>
          </div>

          <div className="space-y-3 max-h-96 overflow-y-auto">
            {events.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                No events received yet. Try sending a message in the main chat to see events.
              </p>
            ) : (
              events.map((event, index) => (
                <div
                  key={index}
                  className="p-3 bg-gray-50 dark:bg-gray-700 rounded border"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900 dark:text-white">
                      {event.type}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {new Date().toLocaleTimeString()}
                    </span>
                  </div>
                  <pre className="text-sm text-gray-700 dark:text-gray-300 overflow-x-auto">
                    {JSON.stringify(event.properties, null, 2)}
                  </pre>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
            How to Test
          </h3>
          <ol className="list-decimal list-inside space-y-2 text-blue-800 dark:text-blue-200">
            <li>Make sure the OpenCode server is running on port 40167</li>
            <li>Go back to the main chat page</li>
            <li>Send a message to see real-time events appear here</li>
            <li>Watch for message.updated and message.part.updated events</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
