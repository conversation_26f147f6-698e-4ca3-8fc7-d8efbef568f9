'use client';

import { useState, useEffect } from 'react';
import { apiService, updateApiService } from '@/lib/api-service';
import { AppLayout } from '@/components/layout/app-layout';
import { ConnectionSetup } from '@/components/connection-setup';
import { ChatInterface } from '@/components/chat/chat-interface';
import { AgentSelectorModal, ModelSelectorModal, HelpModal } from '@/components/modals';
import { commandRouter, type CommandContext } from '@/lib/command-router';
import type { Agent, Model } from '@/components/modals';

export default function Home() {
  const [sessions, setSessions] = useState<any[]>([]);
  const [config, setConfig] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connected' | 'testing'>('disconnected');
  const [currentSessionId, setCurrentSessionId] = useState<string | undefined>(undefined);
  const [sendingMessage, setSendingMessage] = useState(false);

  // Modal states
  const [showAgentSelector, setShowAgentSelector] = useState(false);
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [currentAgent, setCurrentAgent] = useState<string>('general');
  const [currentModel, setCurrentModel] = useState<string>('gpt-4');

  const handleConnect = async (baseUrl: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    setConnectionStatus('testing');

    try {
      // Update the API service with the new base URL
      updateApiService(baseUrl);

      // Test connection
      const configResult = await apiService.getConfig();

      if (!configResult.success) {
        throw new Error(configResult.error || 'Failed to get config');
      }

      setConfig(configResult.data);
      setConnectionStatus('connected');

      // Get sessions
      const sessionsResult = await apiService.getSessions();
      if (sessionsResult.success) {
        setSessions(sessionsResult.data || []);
      }

      console.log('Config:', configResult.data);
      console.log('Sessions:', sessionsResult.data);
      return true;
    } catch (err) {
      console.error('Connection failed:', err);
      setError(err instanceof Error ? err.message : 'Connection failed');
      setConnectionStatus('disconnected');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleSessionSelect = (sessionId: string) => {
    setCurrentSessionId(sessionId);
    console.log('Selected session:', sessionId);
  };

  const handleSessionCreate = async (title: string) => {
    try {
      const result = await apiService.createSession({ title });

      if (result.success) {
        // Refresh sessions list
        const sessionsResult = await apiService.getSessions();
        if (sessionsResult.success) {
          setSessions(sessionsResult.data || []);
          setCurrentSessionId(result.data.id);
        }
      } else {
        throw new Error(result.error || 'Failed to create session');
      }
    } catch (err) {
      console.error('Failed to create session:', err);
      throw err;
    }
  };

  const handleSessionDelete = async (sessionId: string) => {
    try {
      const result = await apiService.deleteSession(sessionId);

      if (result.success) {
        // If we deleted the current session, clear the selection
        if (currentSessionId === sessionId) {
          setCurrentSessionId(undefined);
        }

        // Refresh sessions list
        const sessionsResult = await apiService.getSessions();
        if (sessionsResult.success) {
          setSessions(sessionsResult.data || []);
        }
      } else {
        throw new Error(result.error || 'Failed to delete session');
      }
    } catch (err) {
      console.error('Failed to delete session:', err);
      throw err;
    }
  };

  const handleSessionUpdate = async (sessionId: string, title: string) => {
    try {
      const result = await apiService.updateSession(sessionId, { title });

      if (result.success) {
        // Refresh sessions list
        const sessionsResult = await apiService.getSessions();
        if (sessionsResult.success) {
          setSessions(sessionsResult.data || []);
        }
      } else {
        throw new Error(result.error || 'Failed to update session');
      }
    } catch (err) {
      console.error('Failed to update session:', err);
      throw err;
    }
  };

  const handleSendMessage = async (sessionId: string, message: string) => {
    setSendingMessage(true);
    try {
      const result = await apiService.sendMessage(sessionId, message);
      if (!result.success) {
        throw new Error(result.error || 'Failed to send message');
      }
    } catch (err) {
      console.error('Failed to send message:', err);
      throw err;
    } finally {
      setSendingMessage(false);
    }
  };

  const handleLoadMessages = async (sessionId: string) => {
    try {
      const result = await apiService.getSessionMessages(sessionId);
      if (result.success) {
        // Convert API format to UI format
        const apiMessages = result.data || [];
        return apiMessages.map((apiMessage: any) => ({
          info: {
            id: apiMessage.info.id,
            sessionID: apiMessage.info.sessionID,
            role: apiMessage.info.role,
            time: apiMessage.info.time,
            cost: apiMessage.info.cost,
            modelID: apiMessage.info.modelID,
            providerID: apiMessage.info.providerID,
          },
          parts: apiMessage.parts || []
        }));
      } else {
        throw new Error(result.error || 'Failed to load messages');
      }
    } catch (err) {
      console.error('Failed to load messages:', err);
      throw err;
    }
  };

  const handleExecuteCommand = async (sessionId: string, command: string, args: string) => {
    try {
      // Create command context
      const context: CommandContext = {
        sessionId,
        apiService,
        onShowAgentSelector: () => setShowAgentSelector(true),
        onShowModelSelector: () => setShowModelSelector(true),
        onClearChat: () => {
          // Clear chat will be handled by the TUI endpoint
          console.log('Chat cleared via command');
        },
        onShowHelp: () => setShowHelp(true),
        onShowEditor: () => {
          // TODO: Implement editor modal
          console.log('Editor command executed');
        },
        onShowSessions: () => {
          // TODO: Implement sessions modal or use existing sidebar
          console.log('Sessions command executed');
        }
      };

      // Use command router to execute the command
      const result = await commandRouter.executeCommand(command, args, context);

      if (!result.success) {
        throw new Error(result.message || 'Failed to execute command');
      }

      // Only send to chat if it's a chat command type
      if (result.type === 'chat-message' || result.type === 'session-command') {
        // For chat and session commands, we still need to send them to the API
        const apiResult = await apiService.executeCommand(sessionId, {
          command,
          arguments: args
        });

        if (!apiResult.success) {
          throw new Error(apiResult.error || 'Failed to execute command');
        }

        return apiResult.data;
      }

      // For UI actions, just return success
      return { success: true, type: result.type, message: result.message };
    } catch (err) {
      console.error('Failed to execute command:', err);
      throw err;
    }
  };

  const handleExecuteShell = async (sessionId: string, command: string, agent: string) => {
    try {
      const result = await apiService.executeShell(sessionId, {
        command,
        agent
      });
      if (!result.success) {
        throw new Error(result.error || 'Failed to execute shell command');
      }
    } catch (err) {
      console.error('Failed to execute shell command:', err);
      throw err;
    }
  };

  const handleStopGeneration = () => {
    // TODO: Implement stop generation when available in API
    setSendingMessage(false);
  };

  // Modal handlers
  const handleSelectAgent = (agent: Agent) => {
    setCurrentAgent(agent.id);
    console.log('Selected agent:', agent);
    // TODO: Update session with selected agent
  };

  const handleSelectModel = (model: Model) => {
    setCurrentModel(model.id);
    console.log('Selected model:', model);
    // TODO: Update session with selected model
  };

  useEffect(() => {
    // Try to connect with default URL on startup
    handleConnect('http://127.0.0.1:40167');
  }, []);

  // Show connection setup if not connected
  if (connectionStatus !== 'connected') {
    return (
      <ConnectionSetup
        onConnect={handleConnect}
        connectionStatus={connectionStatus}
        connectionError={error}
        isLoading={loading}
      />
    );
  }

  // Show main app layout when connected
  return (
    <>
      <AppLayout
        connectionStatus={connectionStatus}
        connectionError={error}
        sessions={sessions}
        currentSessionId={currentSessionId}
        onSessionSelect={handleSessionSelect}
        onSessionCreate={handleSessionCreate}
        onSessionDelete={handleSessionDelete}
        onSessionUpdate={handleSessionUpdate}
        onConnect={handleConnect}
        onExecuteCommand={handleExecuteCommand}
        onExecuteShell={handleExecuteShell}
        currentConnection={connectionStatus === 'connected' ? 'http://127.0.0.1:40167' : undefined}
        currentAgent={currentAgent}
        currentModel={currentModel}
        isLoading={loading}
      >
        {/* Chat Interface */}
        <ChatInterface
          sessionId={currentSessionId}
          onSendMessage={handleSendMessage}
          onExecuteCommand={handleExecuteCommand}
          onLoadMessages={handleLoadMessages}
          isLoading={sendingMessage}
          onStop={handleStopGeneration}
          connectionStatus={connectionStatus}
        />
      </AppLayout>

      {/* Command Modals */}
      <AgentSelectorModal
        isOpen={showAgentSelector}
        onClose={() => setShowAgentSelector(false)}
        onSelectAgent={handleSelectAgent}
        currentAgent={currentAgent}
      />

      <ModelSelectorModal
        isOpen={showModelSelector}
        onClose={() => setShowModelSelector(false)}
        onSelectModel={handleSelectModel}
        currentModel={currentModel}
      />

      <HelpModal
        isOpen={showHelp}
        onClose={() => setShowHelp(false)}
      />
    </>
  );
}
