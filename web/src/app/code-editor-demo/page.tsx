'use client';

import React, { useState } from 'react';
import { EnhancedFileManager } from '@/components/files';

export default function CodeEditorDemo() {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Enhanced Code Editor Demo
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            A powerful Monaco Editor-based code viewer and editor with syntax highlighting, 
            IntelliSense, and advanced features.
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              File Manager with Enhanced Code Editor
            </h2>
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              {isOpen ? 'Close' : 'Open'} Editor
            </button>
          </div>

          <div className="h-[600px] border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <EnhancedFileManager 
              isOpen={isOpen} 
              onClose={() => setIsOpen(false)} 
            />
          </div>

          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                🎨 Features
              </h3>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• Syntax highlighting for 30+ languages</li>
                <li>• IntelliSense and auto-completion</li>
                <li>• Multiple themes (VS Dark, Light, GitHub)</li>
                <li>• Code folding and minimap</li>
                <li>• Search and replace</li>
                <li>• Multi-cursor editing</li>
              </ul>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                ⌨️ Keyboard Shortcuts
              </h3>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• <kbd>Ctrl+S</kbd> - Save file</li>
                <li>• <kbd>Ctrl+F</kbd> - Find</li>
                <li>• <kbd>Ctrl+Shift+F</kbd> - Find & Replace</li>
                <li>• <kbd>Ctrl+G</kbd> - Go to line</li>
                <li>• <kbd>Ctrl+D</kbd> - Select next occurrence</li>
                <li>• <kbd>Alt+↑/↓</kbd> - Move lines</li>
              </ul>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                🚀 Performance
              </h3>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• Virtual scrolling for large files</li>
                <li>• Lazy loading of language features</li>
                <li>• Optimized bundle size</li>
                <li>• Smooth animations</li>
                <li>• Auto-save functionality</li>
                <li>• Memory efficient</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
              💡 How to Use
            </h3>
            <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>1. Browse files in the left panel</li>
              <li>2. Click a file to view it with syntax highlighting</li>
              <li>3. Click the edit button (pencil icon) to switch to edit mode</li>
              <li>4. Use the toolbar buttons to customize the editor</li>
              <li>5. Toggle between different view modes and themes</li>
              <li>6. Use keyboard shortcuts for faster editing</li>
              <li>7. Click the performance icon (📈) to monitor performance</li>
              <li>8. Enable optimizations (⚡) for large files</li>
            </ol>
          </div>

          <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">
              🚀 Performance Features
            </h3>
            <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
              <li>• Real-time performance monitoring</li>
              <li>• Automatic optimization suggestions</li>
              <li>• Lazy loading for large files (&gt;500KB)</li>
              <li>• Smart feature disabling for better performance</li>
              <li>• Memory usage tracking</li>
              <li>• Render time optimization</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
