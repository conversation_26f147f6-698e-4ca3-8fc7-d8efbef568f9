import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const baseUrl = searchParams.get('baseUrl') || 'http://127.0.0.1:40167';
  
  try {
    console.log('SSE Proxy: Connecting to', `${baseUrl}/event`);
    
    // Create a readable stream that will proxy the SSE connection
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const response = await fetch(`${baseUrl}/event`, {
            method: 'GET',
            headers: {
              'Accept': 'text/event-stream',
              'Cache-Control': 'no-cache',
              'Connection': 'keep-alive',
            },
          });

          if (!response.ok) {
            throw new Error(`SSE connection failed: ${response.status} ${response.statusText}`);
          }

          if (!response.body) {
            throw new Error('No response body for SSE connection');
          }

          const reader = response.body.getReader();
          const decoder = new TextDecoder();

          try {
            while (true) {
              const { done, value } = await reader.read();
              
              if (done) {
                console.log('SSE Proxy: Stream ended');
                break;
              }

              // Decode the chunk and forward it
              const chunk = decoder.decode(value, { stream: true });
              controller.enqueue(new TextEncoder().encode(chunk));
            }
          } finally {
            reader.releaseLock();
          }
        } catch (error) {
          console.error('SSE Proxy error:', error);
          controller.error(error);
        } finally {
          controller.close();
        }
      },
      
      cancel() {
        console.log('SSE Proxy: Stream cancelled');
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('SSE Proxy setup error:', error);
    return new Response(
      JSON.stringify({ error: error instanceof Error ? error.message : 'Unknown error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
