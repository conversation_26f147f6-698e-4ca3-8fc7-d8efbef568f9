/**
 * API Service Layer for OpenCode Web UI
 * Provides a clean interface for interacting with the OpenCode server
 */

import { sessionPrompt, sessionMessages } from './api-client/sdk.gen';
import { createClient } from './api-client/client';
import type { Message } from './api-client/types.gen';

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface ConnectionConfig {
  baseUrl: string;
  timeout?: number;
}

export interface FileNode {
  name: string;
  path: string;
  absolute: string;
  type: 'file' | 'directory';
  ignored: boolean;
}

export interface FileContent {
  content: string;
  diff?: string;
  patch?: any;
}

export interface SearchMatch {
  path: { text: string };
  lines: { text: string };
  line_number: number;
  absolute_offset: number;
  submatches: Array<{
    match: { text: string };
    start: number;
    end: number;
  }>;
}

export interface Symbol {
  name: string;
  kind: number;
  location: {
    uri: string;
    range: {
      start: { line: number; character: number };
      end: { line: number; character: number };
    };
  };
}

export interface FileStatus {
  path: string;
  added: number;
  removed: number;
  status: 'added' | 'deleted' | 'modified';
}

export interface Command {
  name: string;
  description?: string;
  agent?: string;
  model?: string;
  template: string;
  subtask?: boolean;
}

export interface CommandInput {
  sessionID: string;
  messageID?: string;
  agent?: string;
  model?: string;
  command: string;
  arguments: string;
}

export interface ShellInput {
  sessionID: string;
  agent: string;
  command: string;
}

export interface CommandResult {
  info: any; // AssistantMessage
  parts: any[]; // Part[]
}

export interface ShellResult {
  info: any; // AssistantMessage
}

export class ApiService {
  private config: ConnectionConfig;
  private client: ReturnType<typeof createClient>;

  /**
   * Create a custom fetch function that routes through the proxy
   */
  private createProxyFetch() {
    return async (url: string | URL | Request, init?: RequestInit): Promise<Response> => {
      // Extract the endpoint from the full URL
      const urlStr = url.toString();
      const baseUrl = this.config.baseUrl;

      if (urlStr.startsWith(baseUrl)) {
        const endpoint = urlStr.substring(baseUrl.length);
        const proxyUrl = `/api/proxy?baseUrl=${encodeURIComponent(baseUrl)}&endpoint=${encodeURIComponent(endpoint)}`;

        return fetch(proxyUrl, {
          ...init,
          headers: {
            'Content-Type': 'application/json',
            ...init?.headers,
          },
        });
      }

      // Fallback to normal fetch for other URLs
      return fetch(url, init);
    };
  }

  constructor(config: ConnectionConfig) {
    this.config = {
      timeout: 10000, // 10 seconds default
      ...config
    };

    // Initialize the generated API client with a custom fetch that uses the proxy
    this.client = createClient({
      baseUrl: this.config.baseUrl,
      fetch: this.createProxyFetch(),
    });
  }

  /**
   * Update the base URL for the API service
   */
  updateBaseUrl(baseUrl: string) {
    this.config.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    // Update the client with new base URL
    this.client = createClient({
      baseUrl: this.config.baseUrl,
      fetch: this.createProxyFetch(),
    });
  }

  /**
   * Make a request through the proxy
   */
  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const url = `/api/proxy?baseUrl=${encodeURIComponent(this.config.baseUrl)}&endpoint=${encodeURIComponent(endpoint)}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test connection to the server
   */
  async testConnection(): Promise<ApiResponse<any>> {
    return this.makeRequest('/config');
  }

  /**
   * Get server configuration
   */
  async getConfig(): Promise<ApiResponse<any>> {
    return this.makeRequest('/config');
  }

  /**
   * Get all sessions
   */
  async getSessions(): Promise<ApiResponse<any[]>> {
    return this.makeRequest('/session');
  }

  /**
   * Get a specific session
   */
  async getSession(sessionId: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/session/${sessionId}`);
  }

  /**
   * Create a new session
   */
  async createSession(data: { parentID?: string; title?: string }): Promise<ApiResponse<any>> {
    return this.makeRequest('/session', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * Delete a session
   */
  async deleteSession(sessionId: string): Promise<ApiResponse<boolean>> {
    return this.makeRequest(`/session/${sessionId}`, {
      method: 'DELETE'
    });
  }



  /**
   * Update a session
   */
  async updateSession(sessionId: string, data: { title?: string }): Promise<ApiResponse<any>> {
    return this.makeRequest(`/session/${sessionId}`, {
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  }

  /**
   * Get messages for a session using the generated client
   */
  async getSessionMessages(sessionId: string): Promise<ApiResponse<any[]>> {
    try {
      const response = await sessionMessages({
        client: this.client,
        path: { id: sessionId }
      });

      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      console.error('Failed to get session messages:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get session messages'
      };
    }
  }

  /**
   * Send a message to a session using the generated client
   */
  async sendMessage(sessionId: string, message: string): Promise<ApiResponse<Message>> {
    console.log('Sending message:', { sessionId, message });
    try {
      const response = await sessionPrompt({
        client: this.client,
        path: { id: sessionId },
        body: {
          parts: [
            {
              type: 'text',
              text: message
            }
          ]
        }
      });

      console.log('Message sent successfully:', response);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Failed to send message:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send message'
      };
    }
  }



  /**
   * List files and directories
   */
  async listFiles(path: string = '.'): Promise<FileNode[]> {
    const response = await this.makeRequest<FileNode[]>(`/file?path=${encodeURIComponent(path)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to list files');
    }
    return response.data || [];
  }

  /**
   * Read file content
   */
  async readFile(path: string): Promise<FileContent> {
    const response = await this.makeRequest<FileContent>(`/file/content?path=${encodeURIComponent(path)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to read file');
    }
    return response.data || { content: '' };
  }

  /**
   * Search for text in files
   */
  async searchText(pattern: string): Promise<SearchMatch[]> {
    const response = await this.makeRequest<SearchMatch[]>(`/find?pattern=${encodeURIComponent(pattern)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to search text');
    }
    return response.data || [];
  }

  /**
   * Search for files by name
   */
  async searchFiles(query: string): Promise<string[]> {
    const response = await this.makeRequest<string[]>(`/find/file?query=${encodeURIComponent(query)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to search files');
    }
    return response.data || [];
  }

  /**
   * Search for workspace symbols
   */
  async searchSymbols(query: string): Promise<Symbol[]> {
    const response = await this.makeRequest<Symbol[]>(`/find/symbol?query=${encodeURIComponent(query)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to search symbols');
    }
    return response.data || [];
  }

  /**
   * Get file status (git status)
   */
  async getFileStatus(): Promise<FileStatus[]> {
    const response = await this.makeRequest<FileStatus[]>('/file/status');
    if (!response.success) {
      throw new Error(response.error || 'Failed to get file status');
    }
    return response.data || [];
  }

  /**
   * Get available agents
   */
  async getAgents(): Promise<ApiResponse<any[]>> {
    return this.makeRequest<any[]>('/agent');
  }

  /**
   * Get available providers and models
   */
  async getProviders(): Promise<ApiResponse<any>> {
    return this.makeRequest<any>('/config/providers');
  }

  /**
   * Get available commands
   */
  async getCommands(): Promise<ApiResponse<Command[]>> {
    return this.makeRequest<Command[]>('/command');
  }

  /**
   * Execute a command in a session
   */
  async executeCommand(sessionId: string, commandData: Omit<CommandInput, 'sessionID'>): Promise<ApiResponse<CommandResult>> {
    const commandType = this.getCommandType(commandData.command);

    switch (commandType) {
      case 'built-in':
        return this.executeBuiltInCommand(sessionId, commandData);
      case 'interactive':
        return this.executeInteractiveCommand(sessionId, commandData);
      case 'custom':
        return this.executeCustomCommand(sessionId, commandData);
      default:
        return this.executeBuiltInCommand(sessionId, commandData);
    }
  }

  private getCommandType(command: string): 'built-in' | 'interactive' | 'custom' {
    const builtInCommands = ['clear', 'help', 'status', 'files', 'search', 'init', 'history', 'sessions'];
    const interactiveCommands = ['agents', 'models'];

    if (builtInCommands.includes(command)) {
      return 'built-in';
    } else if (interactiveCommands.includes(command)) {
      return 'interactive';
    } else {
      return 'custom';
    }
  }

  private async executeBuiltInCommand(sessionId: string, commandData: Omit<CommandInput, 'sessionID'>): Promise<ApiResponse<CommandResult>> {
    const commandText = `/${commandData.command}${commandData.arguments ? ' ' + commandData.arguments : ''}`;

    const messageResult = await this.sendMessage(sessionId, commandText);
    if (!messageResult.success) {
      return messageResult as ApiResponse<CommandResult>;
    }

    return {
      success: true,
      data: {
        info: messageResult.data,
        parts: messageResult.data?.parts || []
      }
    };
  }

  private async executeInteractiveCommand(sessionId: string, commandData: Omit<CommandInput, 'sessionID'>): Promise<ApiResponse<CommandResult>> {
    // For interactive commands like /agents and /models, we want to get the data
    // and potentially show UI components, but also send as message for context

    try {
      if (commandData.command === 'agents') {
        // Get agents data for UI
        const agentsResult = await this.getAgents();

        // Also send as message for chat context
        const messageResult = await this.sendMessage(sessionId, `/agents${commandData.arguments ? ' ' + commandData.arguments : ''}`);

        if (messageResult.success) {
          // Enhance the response with agents data for UI
          return {
            success: true,
            data: {
              info: messageResult.data,
              parts: messageResult.data?.parts || [],
              // Add metadata for UI components
              metadata: {
                type: 'agents',
                agents: agentsResult.success ? agentsResult.data : []
              }
            }
          };
        }
      } else if (commandData.command === 'models') {
        // Get providers/models data for UI
        const providersResult = await this.getProviders();

        // Also send as message for chat context
        const messageResult = await this.sendMessage(sessionId, `/models${commandData.arguments ? ' ' + commandData.arguments : ''}`);

        if (messageResult.success) {
          return {
            success: true,
            data: {
              info: messageResult.data,
              parts: messageResult.data?.parts || [],
              metadata: {
                type: 'models',
                providers: providersResult.success ? providersResult.data : { providers: [], default: {} }
              }
            }
          };
        }
      }
    } catch (error) {
      console.warn('Interactive command failed, falling back to message:', error);
    }

    // Fallback to regular message handling
    return this.executeBuiltInCommand(sessionId, commandData);
  }

  private async executeCustomCommand(sessionId: string, commandData: Omit<CommandInput, 'sessionID'>): Promise<ApiResponse<CommandResult>> {
    try {
      const { sessionCommand } = await import('./api-client/sdk.gen');
      const { updateApiClient } = await import('./api-config');

      const client = updateApiClient(this.config.baseUrl);

      const result = await sessionCommand({
        client,
        path: { id: sessionId },
        body: {
          command: commandData.command,
          arguments: commandData.arguments,
          agent: commandData.agent,
          model: commandData.model,
          messageID: commandData.messageID
        }
      });

      if (result.error) {
        throw new Error(result.error.toString());
      }

      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      console.warn('Custom command endpoint failed, trying as message:', error);
      return this.executeBuiltInCommand(sessionId, commandData);
    }
  }

  /**
   * Execute a shell command in a session
   */
  async executeShell(sessionId: string, shellData: Omit<ShellInput, 'sessionID'>): Promise<ApiResponse<ShellResult>> {
    return this.makeRequest<ShellResult>(`/session/${sessionId}/shell`, {
      method: 'POST',
      body: JSON.stringify(shellData)
    });
  }

  /**
   * Revert a message in a session
   */
  async revertMessage(sessionId: string, messageId: string, partId?: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/session/${sessionId}/revert`, {
      method: 'POST',
      body: JSON.stringify({ messageID: messageId, partID: partId })
    });
  }

  /**
   * Get the client instance for direct API calls
   */
  getClient() {
    return this.client;
  }
}

// Default API service instance
export const apiService = new ApiService({
  baseUrl: 'http://127.0.0.1:40167'
});

// Helper function to update the global API service
export function updateApiService(baseUrl: string) {
  apiService.updateBaseUrl(baseUrl);
  return apiService;
}
