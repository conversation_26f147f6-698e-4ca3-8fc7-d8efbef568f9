// Language configurations for better syntax highlighting and IntelliSense
export const configureMonacoLanguages = () => {
  // Only run on client side
  if (typeof window === 'undefined') return;

  const monaco = require('monaco-editor');
  // TypeScript/JavaScript configuration
  monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.Latest,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.CommonJS,
    noEmit: true,
    esModuleInterop: true,
    jsx: monaco.languages.typescript.JsxEmit.React,
    reactNamespace: 'React',
    allowJs: true,
    typeRoots: ['node_modules/@types'],
  });

  monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.Latest,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.CommonJS,
    noEmit: true,
    esModuleInterop: true,
    allowJs: true,
    jsx: monaco.languages.typescript.JsxEmit.React,
  });

  // Enable all TypeScript/JavaScript features
  monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: false,
    noSyntaxValidation: false,
    onlyVisible: false,
  });

  monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: false,
    noSyntaxValidation: false,
    onlyVisible: false,
  });

  // JSON configuration
  monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
    validate: true,
    allowComments: true,
    schemas: [
      {
        uri: 'http://myserver/foo-schema.json',
        fileMatch: ['*'],
        schema: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            version: { type: 'string' },
            description: { type: 'string' },
          },
        },
      },
    ],
  });

  // CSS configuration
  monaco.languages.css.cssDefaults.setOptions({
    validate: true,
    lint: {
      compatibleVendorPrefixes: 'ignore',
      vendorPrefix: 'warning',
      duplicateProperties: 'warning',
      emptyRules: 'warning',
      importStatement: 'ignore',
      boxModel: 'ignore',
      universalSelector: 'ignore',
      zeroUnits: 'ignore',
      fontFaceProperties: 'warning',
      hexColorLength: 'error',
      argumentsInColorFunction: 'error',
      unknownProperties: 'warning',
      ieHack: 'ignore',
      unknownVendorSpecificProperties: 'ignore',
      propertyIgnoredDueToDisplay: 'warning',
      important: 'ignore',
      float: 'ignore',
      idSelector: 'ignore',
    },
  });

  // HTML configuration
  monaco.languages.html.htmlDefaults.setOptions({
    format: {
      tabSize: 2,
      insertSpaces: true,
      wrapLineLength: 120,
      unformatted: 'default',
      contentUnformatted: 'pre,code,textarea',
      indentInnerHtml: false,
      preserveNewLines: true,
      maxPreserveNewLines: undefined,
      indentHandlebars: false,
      endWithNewline: false,
      extraLiners: 'head, body, /html',
      wrapAttributes: 'auto',
    },
    suggest: { html5: true },
  });
};

// Custom themes
export const customThemes = {
  'github-dark': {
    base: 'vs-dark' as const,
    inherit: true,
    rules: [
      { token: 'comment', foreground: '6a737d', fontStyle: 'italic' },
      { token: 'keyword', foreground: 'f97583' },
      { token: 'string', foreground: '9ecbff' },
      { token: 'number', foreground: '79b8ff' },
      { token: 'regexp', foreground: 'dbedff' },
      { token: 'operator', foreground: 'f97583' },
      { token: 'namespace', foreground: 'b392f0' },
      { token: 'type', foreground: 'b392f0' },
      { token: 'struct', foreground: 'b392f0' },
      { token: 'class', foreground: 'b392f0' },
      { token: 'interface', foreground: 'b392f0' },
      { token: 'parameter', foreground: 'ffab70' },
      { token: 'variable', foreground: 'ffab70' },
      { token: 'function', foreground: 'b392f0' },
      { token: 'method', foreground: 'b392f0' },
      { token: 'decorator', foreground: 'ffd33d' },
      { token: 'macro', foreground: 'ffd33d' },
    ],
    colors: {
      'editor.background': '#0d1117',
      'editor.foreground': '#c9d1d9',
      'editor.lineHighlightBackground': '#161b22',
      'editor.selectionBackground': '#264f78',
      'editor.inactiveSelectionBackground': '#3a3d41',
      'editorCursor.foreground': '#c9d1d9',
      'editorWhitespace.foreground': '#484f58',
      'editorIndentGuide.background': '#21262d',
      'editorIndentGuide.activeBackground': '#30363d',
      'editorLineNumber.foreground': '#484f58',
      'editorLineNumber.activeForeground': '#c9d1d9',
    },
  },
  'github-light': {
    base: 'vs' as const,
    inherit: true,
    rules: [
      { token: 'comment', foreground: '6a737d', fontStyle: 'italic' },
      { token: 'keyword', foreground: 'd73a49' },
      { token: 'string', foreground: '032f62' },
      { token: 'number', foreground: '005cc5' },
      { token: 'regexp', foreground: '22863a' },
      { token: 'operator', foreground: 'd73a49' },
      { token: 'namespace', foreground: '6f42c1' },
      { token: 'type', foreground: '6f42c1' },
      { token: 'struct', foreground: '6f42c1' },
      { token: 'class', foreground: '6f42c1' },
      { token: 'interface', foreground: '6f42c1' },
      { token: 'parameter', foreground: 'e36209' },
      { token: 'variable', foreground: 'e36209' },
      { token: 'function', foreground: '6f42c1' },
      { token: 'method', foreground: '6f42c1' },
      { token: 'decorator', foreground: 'e36209' },
      { token: 'macro', foreground: 'e36209' },
    ],
    colors: {
      'editor.background': '#ffffff',
      'editor.foreground': '#24292e',
      'editor.lineHighlightBackground': '#f6f8fa',
      'editor.selectionBackground': '#0366d625',
      'editor.inactiveSelectionBackground': '#e1e4e8',
      'editorCursor.foreground': '#24292e',
      'editorWhitespace.foreground': '#d1d5da',
      'editorIndentGuide.background': '#e1e4e8',
      'editorIndentGuide.activeBackground': '#d1d5da',
      'editorLineNumber.foreground': '#1b1f234d',
      'editorLineNumber.activeForeground': '#24292e',
    },
  },
};

// Register custom themes
export const registerCustomThemes = () => {
  if (typeof window === 'undefined') return;

  const monaco = require('monaco-editor');
  Object.entries(customThemes).forEach(([name, theme]) => {
    monaco.editor.defineTheme(name, theme);
  });
};

// Language-specific configurations
export const languageConfigurations = {
  python: {
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"', notIn: ['string'] },
      { open: "'", close: "'", notIn: ['string', 'comment'] },
      { open: '"""', close: '"""' },
      { open: "'''", close: "'''" },
    ],
    brackets: [
      ['{', '}'],
      ['[', ']'],
      ['(', ')'],
    ],
    comments: {
      lineComment: '#',
      blockComment: ['"""', '"""'],
    },
    folding: {
      markers: {
        start: new RegExp('^\\s*#region\\b'),
        end: new RegExp('^\\s*#endregion\\b'),
      },
    },
  },
  go: {
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"', notIn: ['string'] },
      { open: "'", close: "'", notIn: ['string', 'comment'] },
      { open: '`', close: '`', notIn: ['string', 'comment'] },
    ],
    brackets: [
      ['{', '}'],
      ['[', ']'],
      ['(', ')'],
    ],
    comments: {
      lineComment: '//',
      blockComment: ['/*', '*/'],
    },
  },
  rust: {
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"', notIn: ['string'] },
      { open: "'", close: "'", notIn: ['string', 'comment'] },
    ],
    brackets: [
      ['{', '}'],
      ['[', ']'],
      ['(', ')'],
    ],
    comments: {
      lineComment: '//',
      blockComment: ['/*', '*/'],
    },
  },
};

// Apply language configurations
export const applyLanguageConfigurations = () => {
  if (typeof window === 'undefined') return;

  const monaco = require('monaco-editor');
  Object.entries(languageConfigurations).forEach(([language, config]) => {
    monaco.languages.setLanguageConfiguration(language, config);
  });
};

// Enhanced editor options
export const getEnhancedEditorOptions = (language: string) => {
  const baseOptions = {
    automaticLayout: true,
    scrollBeyondLastLine: false,
    renderWhitespace: 'selection' as const,
    renderControlCharacters: true,
    folding: true,
    foldingStrategy: 'indentation' as const,
    showFoldingControls: 'always' as const,
    matchBrackets: 'always' as const,
    bracketPairColorization: { enabled: true },
    guides: {
      bracketPairs: true,
      indentation: true,
    },
    find: {
      addExtraSpaceOnTop: false,
      autoFindInSelection: 'never' as const,
      seedSearchStringFromSelection: 'always' as const,
    },
    contextmenu: true,
    mouseWheelZoom: true,
    smoothScrolling: true,
    cursorBlinking: 'smooth' as const,
    cursorSmoothCaretAnimation: 'on' as const,
    suggestOnTriggerCharacters: true,
    acceptSuggestionOnEnter: 'on' as const,
    tabCompletion: 'on' as const,
    wordBasedSuggestions: 'allDocuments' as const,
    quickSuggestions: {
      other: true,
      comments: true,
      strings: true,
    },
    parameterHints: { enabled: true },
    codeLens: true,
    lightbulb: { enabled: true },
    hover: { enabled: true },
    links: true,
    colorDecorators: true,
    formatOnPaste: true,
    formatOnType: true,
    autoIndent: 'full' as const,
    detectIndentation: true,
    insertSpaces: true,
    tabSize: 2,
  };

  // Language-specific options
  const languageSpecificOptions: Record<string, any> = {
    typescript: {
      ...baseOptions,
      tabSize: 2,
      insertSpaces: true,
    },
    javascript: {
      ...baseOptions,
      tabSize: 2,
      insertSpaces: true,
    },
    python: {
      ...baseOptions,
      tabSize: 4,
      insertSpaces: true,
    },
    go: {
      ...baseOptions,
      tabSize: 4,
      insertSpaces: false, // Go uses tabs
    },
    rust: {
      ...baseOptions,
      tabSize: 4,
      insertSpaces: true,
    },
    json: {
      ...baseOptions,
      tabSize: 2,
      insertSpaces: true,
      formatOnPaste: true,
      formatOnType: true,
    },
  };

  return languageSpecificOptions[language] || baseOptions;
};

// Initialize Monaco Editor with all configurations
export const initializeMonaco = () => {
  if (typeof window === 'undefined') return;

  configureMonacoLanguages();
  registerCustomThemes();
  applyLanguageConfigurations();
};
