/**
 * Command Router
 * Routes commands to appropriate handlers based on command type
 */

import { ApiService } from './api-service';
import { tuiOpenModels, tuiOpenHelp, tuiClearPrompt } from './api-client/sdk.gen';

export interface CommandContext {
  sessionId?: string;
  apiService: ApiService;
  onShowAgentSelector?: () => void;
  onShowModelSelector?: () => void;
  onClearChat?: () => void;
  onShowHelp?: () => void;
  onShowEditor?: () => void;
  onShowSessions?: () => void;
}

export interface CommandResult {
  success: boolean;
  type: 'ui-action' | 'chat-message' | 'session-command';
  message?: string;
  data?: any;
}

export type CommandType = 'tui' | 'session' | 'chat';

export interface CommandDefinition {
  name: string;
  type: CommandType;
  description: string;
  handler: (args: string, context: CommandContext) => Promise<CommandResult>;
}

export class CommandRouter {
  private commands: Map<string, CommandDefinition> = new Map();

  constructor() {
    this.registerBuiltInCommands();
  }

  private registerBuiltInCommands() {
    // TUI Commands - These trigger UI actions, not chat messages
    this.registerCommand({
      name: 'models',
      type: 'tui',
      description: 'Open model selector',
      handler: this.handleModelsCommand.bind(this)
    });

    this.registerCommand({
      name: 'agents',
      type: 'tui', 
      description: 'Open agent selector',
      handler: this.handleAgentsCommand.bind(this)
    });

    this.registerCommand({
      name: 'clear',
      type: 'tui',
      description: 'Clear chat history',
      handler: this.handleClearCommand.bind(this)
    });

    this.registerCommand({
      name: 'help',
      type: 'tui',
      description: 'Show help dialog',
      handler: this.handleHelpCommand.bind(this)
    });

    this.registerCommand({
      name: 'editor',
      type: 'tui',
      description: 'Open file editor',
      handler: this.handleEditorCommand.bind(this)
    });

    this.registerCommand({
      name: 'sessions',
      type: 'tui',
      description: 'Open session selector',
      handler: this.handleSessionsCommand.bind(this)
    });

    // Session Commands - These affect session state but may also send messages
    this.registerCommand({
      name: 'init',
      type: 'session',
      description: 'Initialize session with agents',
      handler: this.handleInitCommand.bind(this)
    });

    this.registerCommand({
      name: 'status',
      type: 'session',
      description: 'Show session status',
      handler: this.handleStatusCommand.bind(this)
    });

    // Chat Commands - These send messages to the chat
    this.registerCommand({
      name: 'search',
      type: 'chat',
      description: 'Search in files',
      handler: this.handleSearchCommand.bind(this)
    });

    this.registerCommand({
      name: 'files',
      type: 'chat',
      description: 'List files in workspace',
      handler: this.handleFilesCommand.bind(this)
    });

    this.registerCommand({
      name: 'history',
      type: 'chat',
      description: 'Show command history',
      handler: this.handleHistoryCommand.bind(this)
    });
  }

  registerCommand(command: CommandDefinition) {
    this.commands.set(command.name, command);
  }

  async executeCommand(commandName: string, args: string, context: CommandContext): Promise<CommandResult> {
    const command = this.commands.get(commandName);
    
    if (!command) {
      return {
        success: false,
        type: 'chat-message',
        message: `Unknown command: ${commandName}`
      };
    }

    try {
      return await command.handler(args, context);
    } catch (error) {
      console.error(`Command execution failed for ${commandName}:`, error);
      return {
        success: false,
        type: 'chat-message',
        message: `Command failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  getCommandType(commandName: string): CommandType | null {
    const command = this.commands.get(commandName);
    return command?.type || null;
  }

  getAllCommands(): CommandDefinition[] {
    return Array.from(this.commands.values());
  }

  getCommandsByType(type: CommandType): CommandDefinition[] {
    return Array.from(this.commands.values()).filter(cmd => cmd.type === type);
  }

  // TUI Command Handlers
  private async handleModelsCommand(_args: string, context: CommandContext): Promise<CommandResult> {
    try {
      // Use TUI endpoint to open model selector
      await tuiOpenModels({
        client: context.apiService.getClient()
      });

      // Also trigger UI callback if available
      if (context.onShowModelSelector) {
        context.onShowModelSelector();
      }

      return {
        success: true,
        type: 'ui-action',
        message: 'Model selector opened'
      };
    } catch (error) {
      console.error('Failed to open model selector:', error);
      return {
        success: false,
        type: 'ui-action',
        message: 'Failed to open model selector'
      };
    }
  }

  private async handleAgentsCommand(_args: string, context: CommandContext): Promise<CommandResult> {
    try {
      // For agents, we don't have a direct TUI endpoint, so trigger UI callback
      if (context.onShowAgentSelector) {
        context.onShowAgentSelector();
      }

      return {
        success: true,
        type: 'ui-action',
        message: 'Agent selector opened'
      };
    } catch (error) {
      console.error('Failed to open agent selector:', error);
      return {
        success: false,
        type: 'ui-action',
        message: 'Failed to open agent selector'
      };
    }
  }

  private async handleClearCommand(_args: string, context: CommandContext): Promise<CommandResult> {
    try {
      // Use TUI endpoint to clear prompt
      await tuiClearPrompt({
        client: context.apiService.getClient()
      });

      // Also trigger UI callback if available
      if (context.onClearChat) {
        context.onClearChat();
      }

      return {
        success: true,
        type: 'ui-action',
        message: 'Chat cleared'
      };
    } catch (error) {
      console.error('Failed to clear chat:', error);
      return {
        success: false,
        type: 'ui-action',
        message: 'Failed to clear chat'
      };
    }
  }

  private async handleHelpCommand(_args: string, context: CommandContext): Promise<CommandResult> {
    try {
      // Use TUI endpoint to open help
      await tuiOpenHelp({
        client: context.apiService.getClient()
      });

      // Also trigger UI callback if available
      if (context.onShowHelp) {
        context.onShowHelp();
      }

      return {
        success: true,
        type: 'ui-action',
        message: 'Help opened'
      };
    } catch (error) {
      console.error('Failed to open help:', error);
      return {
        success: false,
        type: 'ui-action',
        message: 'Failed to open help'
      };
    }
  }

  private async handleEditorCommand(_args: string, context: CommandContext): Promise<CommandResult> {
    try {
      // Trigger UI callback for editor
      if (context.onShowEditor) {
        context.onShowEditor();
      }

      return {
        success: true,
        type: 'ui-action',
        message: 'Editor opened'
      };
    } catch (error) {
      console.error('Failed to open editor:', error);
      return {
        success: false,
        type: 'ui-action',
        message: 'Failed to open editor'
      };
    }
  }

  private async handleSessionsCommand(_args: string, context: CommandContext): Promise<CommandResult> {
    try {
      // Trigger UI callback for sessions
      if (context.onShowSessions) {
        context.onShowSessions();
      }

      return {
        success: true,
        type: 'ui-action',
        message: 'Sessions opened'
      };
    } catch (error) {
      console.error('Failed to open sessions:', error);
      return {
        success: false,
        type: 'ui-action',
        message: 'Failed to open sessions'
      };
    }
  }

  // Session Command Handlers
  private async handleInitCommand(args: string, context: CommandContext): Promise<CommandResult> {
    if (!context.sessionId) {
      return {
        success: false,
        type: 'session-command',
        message: 'No active session'
      };
    }

    try {
      // This should use the session init endpoint
      const result = await context.apiService.executeCommand(context.sessionId, {
        command: 'init',
        arguments: args
      });

      return {
        success: result.success,
        type: 'session-command',
        message: result.success ? 'Session initialized' : result.error || 'Failed to initialize session',
        data: result.data
      };
    } catch (error) {
      return {
        success: false,
        type: 'session-command',
        message: `Failed to initialize session: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async handleStatusCommand(args: string, context: CommandContext): Promise<CommandResult> {
    if (!context.sessionId) {
      return {
        success: false,
        type: 'session-command',
        message: 'No active session'
      };
    }

    try {
      // This should send a status message to the chat
      const result = await context.apiService.executeCommand(context.sessionId, {
        command: 'status',
        arguments: args
      });

      return {
        success: result.success,
        type: 'chat-message',
        message: result.success ? 'Status requested' : result.error || 'Failed to get status',
        data: result.data
      };
    } catch (error) {
      return {
        success: false,
        type: 'chat-message',
        message: `Failed to get status: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Chat Command Handlers
  private async handleSearchCommand(args: string, context: CommandContext): Promise<CommandResult> {
    if (!context.sessionId) {
      return {
        success: false,
        type: 'chat-message',
        message: 'No active session'
      };
    }

    try {
      const result = await context.apiService.executeCommand(context.sessionId, {
        command: 'search',
        arguments: args
      });

      return {
        success: result.success,
        type: 'chat-message',
        message: result.success ? 'Search executed' : result.error || 'Search failed',
        data: result.data
      };
    } catch (error) {
      return {
        success: false,
        type: 'chat-message',
        message: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async handleFilesCommand(args: string, context: CommandContext): Promise<CommandResult> {
    if (!context.sessionId) {
      return {
        success: false,
        type: 'chat-message',
        message: 'No active session'
      };
    }

    try {
      const result = await context.apiService.executeCommand(context.sessionId, {
        command: 'files',
        arguments: args
      });

      return {
        success: result.success,
        type: 'chat-message',
        message: result.success ? 'Files listed' : result.error || 'Failed to list files',
        data: result.data
      };
    } catch (error) {
      return {
        success: false,
        type: 'chat-message',
        message: `Failed to list files: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async handleHistoryCommand(args: string, context: CommandContext): Promise<CommandResult> {
    if (!context.sessionId) {
      return {
        success: false,
        type: 'chat-message',
        message: 'No active session'
      };
    }

    try {
      const result = await context.apiService.executeCommand(context.sessionId, {
        command: 'history',
        arguments: args
      });

      return {
        success: result.success,
        type: 'chat-message',
        message: result.success ? 'History shown' : result.error || 'Failed to show history',
        data: result.data
      };
    } catch (error) {
      return {
        success: false,
        type: 'chat-message',
        message: `Failed to show history: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

// Export singleton instance
export const commandRouter = new CommandRouter();
