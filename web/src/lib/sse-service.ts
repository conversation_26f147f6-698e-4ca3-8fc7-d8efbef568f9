import type { Event, EventMessageUpdated, EventMessagePartUpdated, EventMessageRemoved } from './api-client/types.gen';

export type MessageUpdateCallback = (event: EventMessageUpdated) => void;
export type MessagePartUpdateCallback = (event: EventMessagePartUpdated) => void;
export type MessageRemovedCallback = (event: EventMessageRemoved) => void;

export interface SSEServiceCallbacks {
  onMessageUpdated?: MessageUpdateCallback;
  onMessagePartUpdated?: MessagePartUpdateCallback;
  onMessageRemoved?: MessageRemovedCallback;
  onError?: (error: any) => void;
  onConnected?: () => void;
  onDisconnected?: () => void;
}

export class SSEService {
  private eventSource: EventSource | null = null;
  private isConnected = false;
  private callbacks: SSEServiceCallbacks = {};
  private baseUrl: string;

  constructor(callbacks: SSEServiceCallbacks = {}, baseUrl: string = 'http://127.0.0.1:40167') {
    this.callbacks = callbacks;
    this.baseUrl = baseUrl;
  }

  /**
   * Connect to the SSE stream via proxy
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      console.warn('SSE service is already connected');
      return;
    }

    try {
      const sseUrl = `/api/sse?baseUrl=${encodeURIComponent(this.baseUrl)}`;
      console.log('Connecting to SSE via proxy:', sseUrl);

      this.eventSource = new EventSource(sseUrl);

      this.eventSource.onopen = () => {
        console.log('SSE connection opened');
        this.isConnected = true;
        this.callbacks.onConnected?.();
      };

      this.eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleEvent(data);
        } catch (error) {
          console.error('Failed to parse SSE event:', error, event.data);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        this.isConnected = false;
        this.callbacks.onError?.(error);
        this.callbacks.onDisconnected?.();
      };

    } catch (error) {
      console.error('Failed to connect to SSE stream:', error);
      this.callbacks.onError?.(error);
      throw error;
    }
  }

  /**
   * Disconnect from the SSE stream
   */
  disconnect(): void {
    if (!this.isConnected) {
      return;
    }

    this.eventSource?.close();
    this.eventSource = null;
    this.isConnected = false;
    this.callbacks.onDisconnected?.();
  }

  /**
   * Update callbacks
   */
  updateCallbacks(callbacks: Partial<SSEServiceCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Check if the service is connected
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }



  /**
   * Handle individual events
   */
  private handleEvent(event: Event): void {
    switch (event.type) {
      case 'server.connected':
        console.log('Server connected event received');
        break;

      case 'message.updated':
        console.log('SSE Service: Message updated event:', event);
        console.log('SSE Service: Calling onMessageUpdated callback:', !!this.callbacks.onMessageUpdated);
        this.callbacks.onMessageUpdated?.(event as EventMessageUpdated);
        break;

      case 'message.part.updated':
        console.log('SSE Service: Message part updated event:', event);
        console.log('SSE Service: Calling onMessagePartUpdated callback:', !!this.callbacks.onMessagePartUpdated);
        this.callbacks.onMessagePartUpdated?.(event as EventMessagePartUpdated);
        break;

      case 'message.removed':
        console.log('SSE Service: Message removed event:', event);
        console.log('SSE Service: Calling onMessageRemoved callback:', !!this.callbacks.onMessageRemoved);
        this.callbacks.onMessageRemoved?.(event as EventMessageRemoved);
        break;

      case 'session.updated':
      case 'session.deleted':
      case 'session.error':
        // These could be handled by other parts of the app
        console.log(`Session event: ${event.type}`, event);
        break;

      default:
        console.log('Unhandled SSE event:', event);
        break;
    }
  }
}

// Global SSE service instance
let globalSSEService: SSEService | null = null;

/**
 * Get or create the global SSE service instance
 */
export function getSSEService(callbacks?: SSEServiceCallbacks, baseUrl?: string): SSEService {
  if (!globalSSEService) {
    globalSSEService = new SSEService(callbacks, baseUrl);
  } else if (callbacks) {
    globalSSEService.updateCallbacks(callbacks);
  }

  return globalSSEService;
}

/**
 * Cleanup the global SSE service
 */
export function cleanupSSEService(): void {
  if (globalSSEService) {
    globalSSEService.disconnect();
    globalSSEService = null;
  }
}
