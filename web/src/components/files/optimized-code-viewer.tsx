'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Editor } from '@monaco-editor/react';
import { 
  File, 
  Download, 
  Copy, 
  Edit3, 
  Eye, 
  EyeOff,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  X,
  Check,
  AlertCircle,
  Settings,
  Maximize2,
  Minimize2,
  Map,
  Zap,
  TrendingUp,
  ChevronDown
} from 'lucide-react';
import { apiService } from '@/lib/api-service';
import { usePerformanceMonitor, useLazyFileLoading } from '@/hooks/use-performance-monitor';

interface FileNode {
  name: string;
  path: string;
  absolute: string;
  type: 'file' | 'directory';
  ignored: boolean;
}

interface FileContent {
  content: string;
  diff?: string;
  patch?: any;
}

interface OptimizedCodeViewerProps {
  file: FileNode | null;
  onClose?: () => void;
  onEdit?: (file: FileNode) => void;
  readOnly?: boolean;
  theme?: 'vs-dark' | 'vs-light' | 'hc-black';
}

export function OptimizedCodeViewer({ 
  file, 
  onClose, 
  onEdit, 
  readOnly = true, 
  theme = 'vs-dark' 
}: OptimizedCodeViewerProps) {
  const [content, setContent] = useState<FileContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [fontSize, setFontSize] = useState(14);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showMinimap, setShowMinimap] = useState(true);
  const [wordWrap, setWordWrap] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentTheme, setCurrentTheme] = useState(theme);
  const [showPerformancePanel, setShowPerformancePanel] = useState(false);
  const [useOptimizations, setUseOptimizations] = useState(false);
  
  const editorRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Performance monitoring
  const {
    metrics,
    isOptimized,
    formatFileSize,
    getOptimizationSuggestions,
    getRecommendedEditorOptions,
  } = usePerformanceMonitor(content?.content || '', {
    threshold: 500 * 1024, // 500KB threshold
    enableMemoryMonitoring: true,
    onPerformanceIssue: (metrics) => {
      console.warn('Performance issue detected:', metrics);
      setUseOptimizations(true);
    },
  });

  // Lazy loading for large files
  const {
    loadedLines,
    totalLines,
    isFullyLoaded,
    loadMoreLines,
    getVisibleContent,
    getRemainingLines,
    progress,
  } = useLazyFileLoading(content?.content || '', 2000);

  // Memoized editor options
  const editorOptions = useMemo(() => {
    const baseOptions = {
      automaticLayout: true,
      scrollBeyondLastLine: false,
      renderWhitespace: 'selection' as const,
      renderControlCharacters: true,
      folding: true,
      foldingStrategy: 'indentation' as const,
      showFoldingControls: 'always' as const,
      matchBrackets: 'always' as const,
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        indentation: true,
      },
    };

    const performanceOptions = useOptimizations ? getRecommendedEditorOptions() : {};

    return {
      ...baseOptions,
      ...performanceOptions,
      fontSize: fontSize,
      lineNumbers: showLineNumbers ? 'on' : 'off',
      minimap: { enabled: showMinimap && !useOptimizations },
      wordWrap: wordWrap ? 'on' : 'off',
      readOnly: readOnly,
    };
  }, [
    fontSize,
    showLineNumbers,
    showMinimap,
    wordWrap,
    readOnly,
    useOptimizations,
    getRecommendedEditorOptions,
  ]);

  // Memoized content for performance
  const displayContent = useMemo(() => {
    if (!content?.content) return '';
    return metrics?.isLargeFile && !isFullyLoaded ? getVisibleContent() : content.content;
  }, [content?.content, metrics?.isLargeFile, isFullyLoaded, getVisibleContent]);

  useEffect(() => {
    if (file && file.type === 'file') {
      loadFileContent();
    } else {
      setContent(null);
      setError(null);
    }
  }, [file]);

  const loadFileContent = async () => {
    if (!file) return;

    try {
      setLoading(true);
      setError(null);
      const fileContent = await apiService.readFile(file.path);
      setContent(fileContent);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load file content');
      console.error('Failed to load file content:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async () => {
    if (!content?.content) return;

    try {
      await navigator.clipboard.writeText(content.content);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy content:', err);
    }
  };

  const handleDownload = () => {
    if (!content?.content || !file) return;

    const blob = new Blob([content.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getLanguageFromExtension = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'json': 'json',
      'md': 'markdown',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'html': 'html',
      'htm': 'html',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'sql': 'sql',
      'go': 'go',
      'rs': 'rust',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cc': 'cpp',
      'cxx': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
      'hxx': 'cpp',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'hs': 'haskell',
      'elm': 'elm',
      'dart': 'dart',
      'lua': 'lua',
      'r': 'r',
      'dockerfile': 'dockerfile',
      'makefile': 'makefile',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
    };
    return languageMap[ext || ''] || 'plaintext';
  };

  const handleEditorDidMount = useCallback(async (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Initialize Monaco with enhanced configurations
    try {
      const { initializeMonaco } = await import('@/lib/monaco-config');
      initializeMonaco();
    } catch (error) {
      console.warn('Failed to load Monaco config:', error);
    }

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
      editor.getAction('actions.find').run();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyG, () => {
      editor.getAction('editor.action.goToLine').run();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
      editor.getAction('editor.action.startFindReplaceAction').run();
    });

    // Add performance monitoring
    editor.onDidChangeModelContent(() => {
      // Debounced performance check
      setTimeout(() => {
        if (metrics && !isOptimized) {
          console.log('Performance suggestions:', getOptimizationSuggestions());
        }
      }, 500);
    });
  }, [metrics, isOptimized, getOptimizationSuggestions]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (containerRef.current) {
      if (!isFullscreen) {
        containerRef.current.requestFullscreen?.();
      } else {
        document.exitFullscreen?.();
      }
    }
  };

  const cycleTheme = () => {
    const themes = ['vs-dark', 'vs-light', 'hc-black'] as const;
    const currentIndex = themes.indexOf(currentTheme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setCurrentTheme(nextTheme);
  };

  if (!file) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <File className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">No file selected</p>
          <p className="text-sm">Select a file from the explorer to view its content</p>
        </div>
      </div>
    );
  }

  if (file.type === 'directory') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <File className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">Directory selected</p>
          <p className="text-sm">Select a file to view its content</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className={`flex-1 flex flex-col bg-white dark:bg-gray-900 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}
    >
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3 min-w-0 flex-1">
            <File className="w-4 h-4 text-gray-600 dark:text-gray-400 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h2 className="text-sm font-semibold text-gray-900 dark:text-white truncate flex items-center">
                {file.name}
                {metrics && (
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                    isOptimized 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {isOptimized ? 'Optimized' : 'Performance Issue'}
                  </span>
                )}
              </h2>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {file.path}
                {metrics && (
                  <span className="ml-2">
                    • {formatFileSize(metrics.fileSize)} • {metrics.lineCount} lines
                  </span>
                )}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-1 flex-shrink-0">
            {/* Performance toggle */}
            <button
              onClick={() => setShowPerformancePanel(!showPerformancePanel)}
              className={`p-1.5 rounded ${
                showPerformancePanel
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
              title="Performance panel"
            >
              <TrendingUp className="w-3.5 h-3.5" />
            </button>

            {/* Optimization toggle */}
            <button
              onClick={() => setUseOptimizations(!useOptimizations)}
              className={`p-1.5 rounded ${
                useOptimizations
                  ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
              title={useOptimizations ? 'Disable optimizations' : 'Enable optimizations'}
            >
              <Zap className="w-3.5 h-3.5" />
            </button>

            {/* Font size controls */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setFontSize(Math.max(10, fontSize - 2))}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Decrease font size"
              >
                <ZoomOut className="w-3.5 h-3.5" />
              </button>
              <span className="text-xs text-gray-500 dark:text-gray-400 min-w-[2.5rem] text-center">
                {fontSize}px
              </span>
              <button
                onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Increase font size"
              >
                <ZoomIn className="w-3.5 h-3.5" />
              </button>
            </div>

            {/* Theme toggle */}
            <button
              onClick={cycleTheme}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Toggle theme"
            >
              <Settings className="w-3.5 h-3.5" />
            </button>

            {/* Line numbers toggle */}
            <button
              onClick={() => setShowLineNumbers(!showLineNumbers)}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title={showLineNumbers ? "Hide line numbers" : "Show line numbers"}
            >
              {showLineNumbers ? <EyeOff className="w-3.5 h-3.5" /> : <Eye className="w-3.5 h-3.5" />}
            </button>

            {/* Minimap toggle */}
            <button
              onClick={() => setShowMinimap(!showMinimap)}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title={showMinimap ? "Hide minimap" : "Show minimap"}
            >
              <Map className="w-3.5 h-3.5" />
            </button>

            {/* Fullscreen toggle */}
            <button
              onClick={toggleFullscreen}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? <Minimize2 className="w-3.5 h-3.5" /> : <Maximize2 className="w-3.5 h-3.5" />}
            </button>

            {/* Copy button */}
            <button
              onClick={handleCopy}
              disabled={!content?.content}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Copy content"
            >
              {copySuccess ? <Check className="w-3.5 h-3.5 text-green-600" /> : <Copy className="w-3.5 h-3.5" />}
            </button>

            {/* Download button */}
            <button
              onClick={handleDownload}
              disabled={!content?.content}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Download file"
            >
              <Download className="w-3.5 h-3.5" />
            </button>

            {/* Edit button */}
            <button
              onClick={() => onEdit?.(file)}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Edit file"
            >
              <Edit3 className="w-3.5 h-3.5" />
            </button>

            {/* Close button */}
            {onClose && (
              <button
                onClick={onClose}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Close"
              >
                <X className="w-3.5 h-3.5" />
              </button>
            )}
          </div>
        </div>

        {/* Performance Panel */}
        {showPerformancePanel && metrics && (
          <div className="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Render Time:</span>
                <div className={`font-mono ${metrics.renderTime > 500 ? 'text-red-600' : 'text-green-600'}`}>
                  {metrics.renderTime.toFixed(2)}ms
                </div>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">File Size:</span>
                <div className={`font-mono ${metrics.isLargeFile ? 'text-yellow-600' : 'text-green-600'}`}>
                  {formatFileSize(metrics.fileSize)}
                </div>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Lines:</span>
                <div className={`font-mono ${metrics.lineCount > 5000 ? 'text-yellow-600' : 'text-green-600'}`}>
                  {metrics.lineCount.toLocaleString()}
                </div>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Status:</span>
                <div className={`font-mono ${isOptimized ? 'text-green-600' : 'text-red-600'}`}>
                  {isOptimized ? 'Optimized' : 'Needs Optimization'}
                </div>
              </div>
            </div>
            
            {getOptimizationSuggestions().length > 0 && (
              <div className="mt-3">
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Suggestions:</div>
                <ul className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
                  {getOptimizationSuggestions().map((suggestion, index) => (
                    <li key={index}>• {suggestion}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading file content...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-red-600 dark:text-red-400">
              <AlertCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Failed to load file</p>
              <p className="text-sm">{error}</p>
              <button
                onClick={loadFileContent}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <RotateCcw className="w-4 h-4 inline mr-2" />
                Retry
              </button>
            </div>
          </div>
        ) : content ? (
          <div className="h-full flex flex-col">
            <Editor
              height="100%"
              language={getLanguageFromExtension(file.name)}
              value={displayContent}
              theme={currentTheme}
              onMount={handleEditorDidMount}
              options={editorOptions}
            />
            
            {/* Load more button for large files */}
            {metrics?.isLargeFile && !isFullyLoaded && (
              <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    Showing {loadedLines.toLocaleString()} of {totalLines.toLocaleString()} lines 
                    ({progress.toFixed(1)}%)
                  </div>
                  <button
                    onClick={loadMoreLines}
                    className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    <ChevronDown className="w-4 h-4 mr-1" />
                    Load {Math.min(getRemainingLines(), 2000).toLocaleString()} more lines
                  </button>
                </div>
                <div className="mt-2 w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1">
                  <div 
                    className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        ) : null}
      </div>

      {/* Footer */}
      {content && (
        <div className="p-2 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400 flex justify-between">
          <span>
            {displayContent.split('\n').length} lines, {displayContent.length} characters
            {useOptimizations && <span className="text-green-600 ml-2">• Optimized</span>}
          </span>
          <span>
            {getLanguageFromExtension(file.name)} • {currentTheme}
            {metrics && <span className="ml-2">• {metrics.renderTime.toFixed(0)}ms</span>}
          </span>
        </div>
      )}
    </div>
  );
}
