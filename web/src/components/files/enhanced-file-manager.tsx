'use client';

import React, { useState } from 'react';
import { 
  Search, 
  X, 
  Maximize2, 
  Minimize2,
  Split,
  FileText,
  Folder,
  Edit3,
  Eye,
  Code,
  Settings
} from 'lucide-react';
import dynamic from 'next/dynamic';
import { FileExplorer } from './file-explorer';
import { FileViewer } from './file-viewer';
import { FileSearch } from './file-search';

// Dynamically import Monaco Editor components to avoid SSR issues
const CodeViewer = dynamic(() => import('./code-viewer').then(mod => ({ default: mod.CodeViewer })), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-full">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
});

const CodeEditor = dynamic(() => import('./code-editor').then(mod => ({ default: mod.CodeEditor })), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-full">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
});

interface FileNode {
  name: string;
  path: string;
  absolute: string;
  type: 'file' | 'directory';
  ignored: boolean;
}

interface EnhancedFileManagerProps {
  isOpen?: boolean;
  onClose?: () => void;
}

type ViewMode = 'split' | 'explorer-only' | 'viewer-only' | 'editor-only';
type EditorMode = 'view' | 'edit';

export function EnhancedFileManager({ isOpen = true, onClose }: EnhancedFileManagerProps) {
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('split');
  const [editorMode, setEditorMode] = useState<EditorMode>('view');
  const [showSearch, setShowSearch] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [theme, setTheme] = useState<'vs-dark' | 'vs-light' | 'hc-black'>('vs-dark');
  const [useMonacoEditor, setUseMonacoEditor] = useState(true);

  const handleFileSelect = (file: FileNode) => {
    setSelectedFile(file);
    if (viewMode === 'explorer-only') {
      setViewMode('split');
    }
    // Default to view mode when selecting a new file
    setEditorMode('view');
  };

  const handleFileOpen = (file: FileNode) => {
    setSelectedFile(file);
    setViewMode('viewer-only');
    setEditorMode('view');
  };

  const handleSearchFileSelect = (path: string, lineNumber?: number) => {
    // Create a file node from the path
    const fileName = path.split('/').pop() || path;
    const fileNode: FileNode = {
      name: fileName,
      path: path,
      absolute: path,
      type: 'file',
      ignored: false
    };
    
    setSelectedFile(fileNode);
    setShowSearch(false);
    setEditorMode('view');
    
    // TODO: If lineNumber is provided, scroll to that line in the viewer
    if (lineNumber) {
      console.log(`Navigate to line ${lineNumber} in ${path}`);
    }
  };

  const handleCloseViewer = () => {
    if (viewMode === 'viewer-only' || viewMode === 'editor-only') {
      setViewMode('split');
    } else {
      setSelectedFile(null);
    }
    setEditorMode('view');
  };

  const handleEditFile = (file: FileNode) => {
    setSelectedFile(file);
    setEditorMode('edit');
    if (viewMode === 'explorer-only') {
      setViewMode('split');
    }
  };

  const handleSaveFile = (file: FileNode, content: string) => {
    console.log('Saving file:', file.path, 'with content length:', content.length);
    // TODO: Implement actual file saving
  };

  const toggleViewMode = () => {
    const modes: ViewMode[] = ['split', 'explorer-only', 'viewer-only'];
    const currentIndex = modes.indexOf(viewMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setViewMode(modes[nextIndex]);
  };

  const toggleEditorMode = () => {
    setEditorMode(editorMode === 'view' ? 'edit' : 'view');
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const cycleTheme = () => {
    const themes = ['vs-dark', 'vs-light', 'hc-black'] as const;
    const currentIndex = themes.indexOf(theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setTheme(nextTheme);
  };

  const getViewModeIcon = () => {
    switch (viewMode) {
      case 'split':
        return <Split className="w-4 h-4" />;
      case 'explorer-only':
        return <Folder className="w-4 h-4" />;
      case 'viewer-only':
        return <Eye className="w-4 h-4" />;
      case 'editor-only':
        return <Edit3 className="w-4 h-4" />;
    }
  };

  const getViewModeTitle = () => {
    switch (viewMode) {
      case 'split':
        return 'Split view';
      case 'explorer-only':
        return 'Explorer only';
      case 'viewer-only':
        return 'Viewer only';
      case 'editor-only':
        return 'Editor only';
    }
  };

  const getEditorModeIcon = () => {
    return editorMode === 'view' ? <Eye className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />;
  };

  const getEditorModeTitle = () => {
    return editorMode === 'view' ? 'Switch to edit mode' : 'Switch to view mode';
  };

  if (!isOpen) return null;

  return (
    <div className={`
      ${isFullscreen 
        ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900' 
        : 'h-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg'
      } 
      flex flex-col overflow-hidden
    `}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-3">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Code Editor
          </h2>
          {selectedFile && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">•</span>
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {selectedFile.name}
              </span>
              <span className={`px-2 py-1 text-xs rounded-full ${
                editorMode === 'edit'
                  ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              }`}>
                {editorMode === 'edit' ? 'Editing' : 'Viewing'} • {useMonacoEditor ? 'Monaco' : 'Basic'}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Search toggle */}
          <button
            onClick={() => setShowSearch(!showSearch)}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
            title="Toggle search"
          >
            <Search className="w-4 h-4" />
          </button>

          {/* Editor mode toggle */}
          {selectedFile && selectedFile.type === 'file' && (
            <button
              onClick={toggleEditorMode}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title={getEditorModeTitle()}
            >
              {getEditorModeIcon()}
            </button>
          )}

          {/* View mode toggle */}
          <button
            onClick={toggleViewMode}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
            title={getViewModeTitle()}
          >
            {getViewModeIcon()}
          </button>

          {/* Monaco Editor toggle */}
          <button
            onClick={() => setUseMonacoEditor(!useMonacoEditor)}
            className={`p-2 rounded ${
              useMonacoEditor
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700'
            }`}
            title={useMonacoEditor ? 'Switch to basic viewer' : 'Switch to Monaco Editor'}
          >
            <Code className="w-4 h-4" />
          </button>

          {/* Theme toggle */}
          <button
            onClick={cycleTheme}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
            title="Toggle theme"
          >
            <Settings className="w-4 h-4" />
          </button>

          {/* Fullscreen toggle */}
          <button
            onClick={toggleFullscreen}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
            title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
          >
            {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>

          {/* Close button */}
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Close"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Search Panel */}
      {showSearch && (
        <div className="border-b border-gray-200 dark:border-gray-700">
          <FileSearch onFileSelect={handleSearchFileSelect} />
        </div>
      )}

      {/* Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* File Explorer */}
        {(viewMode === 'split' || viewMode === 'explorer-only') && (
          <div className={`${
            viewMode === 'split' ? 'w-1/3 min-w-[300px]' : 'flex-1'
          } border-r border-gray-200 dark:border-gray-700`}>
            <FileExplorer
              onFileSelect={handleFileSelect}
              onFileOpen={handleFileOpen}
              selectedPath={selectedFile?.path}
            />
          </div>
        )}

        {/* File Viewer/Editor */}
        {(viewMode === 'split' || viewMode === 'viewer-only' || viewMode === 'editor-only') && (
          <div className="flex-1 flex">
            {useMonacoEditor ? (
              editorMode === 'edit' ? (
                <CodeEditor
                  file={selectedFile}
                  onClose={handleCloseViewer}
                  onSave={handleSaveFile}
                  theme={theme}
                  autoSave={true}
                  autoSaveDelay={2000}
                />
              ) : (
                <CodeViewer
                  file={selectedFile}
                  onClose={handleCloseViewer}
                  onEdit={handleEditFile}
                  readOnly={true}
                  theme={theme}
                />
              )
            ) : (
              <FileViewer
                file={selectedFile}
                onClose={handleCloseViewer}
                onEdit={handleEditFile}
              />
            )}
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-4">
            <span>Mode: {viewMode.replace('-', ' ')}</span>
            {selectedFile && (
              <>
                <span>•</span>
                <span>{editorMode === 'edit' ? 'Editing' : 'Viewing'}: {selectedFile.name}</span>
              </>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <span>Theme: {theme}</span>
            <span>•</span>
            <span>{useMonacoEditor ? 'Monaco Editor' : 'Basic Viewer'}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
