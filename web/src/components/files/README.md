# Enhanced Code Editor Components

A comprehensive set of React components built with Monaco Editor for viewing and editing code files with advanced features, performance optimizations, and a smooth user experience.

## 🚀 Features

### Core Components

- **CodeViewer**: Read-only code viewer with syntax highlighting
- **CodeEditor**: Full-featured code editor with auto-save and validation
- **OptimizedCodeViewer**: Performance-optimized viewer for large files
- **EnhancedFileManager**: Complete file management interface

### Advanced Features

- ✨ **Syntax Highlighting**: Support for 30+ programming languages
- 🧠 **IntelliSense**: Auto-completion, parameter hints, and error detection
- 🎨 **Multiple Themes**: VS Dark, VS Light, High Contrast, and custom GitHub themes
- 🔍 **Advanced Search**: Find, replace, go-to-line, and multi-cursor editing
- 📁 **Code Folding**: Collapse/expand code blocks and regions
- 🗺️ **Minimap**: Bird's eye view of the entire file
- ⌨️ **Keyboard Shortcuts**: VS Code-compatible shortcuts
- 💾 **Auto-save**: Configurable auto-save with unsaved changes tracking
- 🔄 **Real-time Collaboration**: Live editing capabilities

### Performance Optimizations

- 📊 **Performance Monitoring**: Real-time render time and memory usage tracking
- ⚡ **Smart Optimizations**: Automatic feature disabling for large files
- 🔄 **Lazy Loading**: Progressive loading for files >500KB
- 🎯 **Virtual Scrolling**: Efficient rendering of large files
- 📈 **Performance Suggestions**: Automatic optimization recommendations

## 🛠️ Installation

```bash
# Install dependencies
bun add @monaco-editor/react monaco-editor

# Install the components (already included in this project)
```

## 📖 Usage

### Basic Code Viewer

```tsx
import { CodeViewer } from '@/components/files';

function MyComponent() {
  const file = {
    name: 'example.ts',
    path: '/path/to/example.ts',
    absolute: '/absolute/path/to/example.ts',
    type: 'file' as const,
    ignored: false
  };

  return (
    <CodeViewer
      file={file}
      theme="vs-dark"
      readOnly={true}
      onEdit={(file) => console.log('Edit:', file)}
    />
  );
}
```

### Code Editor with Auto-save

```tsx
import { CodeEditor } from '@/components/files';

function MyEditor() {
  return (
    <CodeEditor
      file={file}
      theme="vs-dark"
      autoSave={true}
      autoSaveDelay={2000}
      onSave={(file, content) => {
        console.log('Saving:', file.path, content);
      }}
    />
  );
}
```

### Enhanced File Manager

```tsx
import { EnhancedFileManager } from '@/components/files';

function MyApp() {
  return (
    <div className="h-screen">
      <EnhancedFileManager
        isOpen={true}
        onClose={() => console.log('Closed')}
      />
    </div>
  );
}
```

### Performance-Optimized Viewer

```tsx
import { OptimizedCodeViewer } from '@/components/files';

function LargeFileViewer() {
  return (
    <OptimizedCodeViewer
      file={largeFile}
      theme="vs-dark"
      onEdit={(file) => console.log('Edit:', file)}
    />
  );
}
```

## ⌨️ Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+S` | Save file |
| `Ctrl+F` | Find |
| `Ctrl+Shift+F` | Find and Replace |
| `Ctrl+G` | Go to Line |
| `Ctrl+D` | Select Next Occurrence |
| `Ctrl+/` | Toggle Line Comment |
| `Alt+↑/↓` | Move Lines Up/Down |
| `Ctrl+Shift+K` | Delete Line |
| `Ctrl+Z` | Undo |
| `Ctrl+Shift+Z` | Redo |

## 🎨 Themes

### Built-in Themes
- `vs-dark` - Dark theme (default)
- `vs-light` - Light theme
- `hc-black` - High contrast theme

### Custom Themes
- `github-dark` - GitHub dark theme
- `github-light` - GitHub light theme

## 🔧 Configuration

### Monaco Editor Configuration

The components use enhanced Monaco Editor configurations located in `@/lib/monaco-config.ts`:

```typescript
import { initializeMonaco, getEnhancedEditorOptions } from '@/lib/monaco-config';

// Initialize Monaco with all enhancements
initializeMonaco();

// Get language-specific options
const options = getEnhancedEditorOptions('typescript');
```

### Performance Monitoring

```typescript
import { usePerformanceMonitor } from '@/hooks/use-performance-monitor';

const {
  metrics,
  isOptimized,
  getOptimizationSuggestions,
  getRecommendedEditorOptions
} = usePerformanceMonitor(content, {
  threshold: 500 * 1024, // 500KB
  enableMemoryMonitoring: true,
  onPerformanceIssue: (metrics) => {
    console.warn('Performance issue:', metrics);
  }
});
```

## 🚀 Performance Features

### Automatic Optimizations

The components automatically optimize performance for large files by:

- Disabling minimap for files >500KB
- Reducing syntax highlighting complexity
- Disabling unnecessary editor features
- Implementing lazy loading for large files
- Using virtual scrolling for better memory usage

### Performance Monitoring

Real-time monitoring includes:

- Render time tracking
- Memory usage monitoring
- File size analysis
- Line count optimization
- Performance suggestions

### Lazy Loading

For large files, the components implement:

- Progressive loading (2000 lines at a time)
- Load more functionality
- Progress indicators
- Memory-efficient rendering

## 🎯 Language Support

Supported languages with enhanced features:

- **JavaScript/TypeScript**: Full IntelliSense, error detection
- **Python**: Syntax highlighting, auto-indentation
- **Go**: Language-specific formatting
- **Rust**: Advanced syntax support
- **JSON**: Schema validation
- **CSS/SCSS**: Property suggestions
- **HTML**: Tag completion
- **Markdown**: Live preview capabilities
- **And 20+ more languages**

## 🔍 Advanced Features

### Search and Replace
- Regex support
- Case-sensitive search
- Whole word matching
- Multi-file search (coming soon)

### Code Navigation
- Go to definition
- Find references
- Symbol search
- Breadcrumb navigation

### Editing Features
- Multi-cursor editing
- Block selection
- Code formatting
- Auto-indentation
- Bracket matching

## 📊 Performance Metrics

The components track and display:

- **Render Time**: Time to render the editor
- **File Size**: Current file size with formatting
- **Line Count**: Total lines in the file
- **Memory Usage**: JavaScript heap usage
- **Optimization Status**: Current optimization level

## 🐛 Troubleshooting

### Common Issues

1. **Monaco Editor not loading**
   - Ensure Next.js configuration includes Monaco webpack settings
   - Check that all dependencies are installed

2. **Performance issues with large files**
   - Enable optimizations using the ⚡ button
   - Use OptimizedCodeViewer for files >500KB
   - Check performance panel for suggestions

3. **Syntax highlighting not working**
   - Verify file extension is supported
   - Check Monaco language configuration

### Debug Mode

Enable debug logging:

```typescript
// In your component
console.log('Performance metrics:', metrics);
console.log('Optimization suggestions:', getOptimizationSuggestions());
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
