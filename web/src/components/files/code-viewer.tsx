'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Editor } from '@monaco-editor/react';
import {
  File,
  Download,
  Copy,
  Edit3,
  Eye,
  EyeOff,
  Search,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  X,
  Check,
  AlertCircle,
  Settings,
  Maximize2,
  Minimize2,
  Map
} from 'lucide-react';
import { apiService } from '@/lib/api-service';

interface FileNode {
  name: string;
  path: string;
  absolute: string;
  type: 'file' | 'directory';
  ignored: boolean;
}

interface FileContent {
  content: string;
  diff?: string;
  patch?: any;
}

interface CodeViewerProps {
  file: FileNode | null;
  onClose?: () => void;
  onEdit?: (file: FileNode) => void;
  readOnly?: boolean;
  theme?: 'vs-dark' | 'vs-light' | 'hc-black';
}

export function CodeViewer({ file, onClose, onEdit, readOnly = true, theme = 'vs-dark' }: CodeViewerProps) {
  const [content, setContent] = useState<FileContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [fontSize, setFontSize] = useState(14);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showMinimap, setShowMinimap] = useState(true);
  const [wordWrap, setWordWrap] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentTheme, setCurrentTheme] = useState(theme);
  
  const editorRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (file && file.type === 'file') {
      loadFileContent();
    } else {
      setContent(null);
      setError(null);
    }
  }, [file]);

  const loadFileContent = async () => {
    if (!file) return;

    try {
      setLoading(true);
      setError(null);
      const fileContent = await apiService.readFile(file.path);
      setContent(fileContent);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load file content');
      console.error('Failed to load file content:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async () => {
    if (!content?.content) return;

    try {
      await navigator.clipboard.writeText(content.content);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy content:', err);
    }
  };

  const handleDownload = () => {
    if (!content?.content || !file) return;

    const blob = new Blob([content.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getLanguageFromExtension = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'json': 'json',
      'md': 'markdown',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'html': 'html',
      'htm': 'html',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'sql': 'sql',
      'go': 'go',
      'rs': 'rust',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cc': 'cpp',
      'cxx': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
      'hxx': 'cpp',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'hs': 'haskell',
      'elm': 'elm',
      'dart': 'dart',
      'lua': 'lua',
      'r': 'r',
      'dockerfile': 'dockerfile',
      'makefile': 'makefile',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
    };
    return languageMap[ext || ''] || 'plaintext';
  };

  const handleEditorDidMount = useCallback(async (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Initialize Monaco with enhanced configurations
    try {
      const { initializeMonaco, getEnhancedEditorOptions } = await import('@/lib/monaco-config');
      initializeMonaco();

      // Get enhanced options for the current language
      const language = getLanguageFromExtension(file?.name || '');
      const enhancedOptions = getEnhancedEditorOptions(language);

      // Configure editor options
      editor.updateOptions({
        ...enhancedOptions,
        fontSize: fontSize,
        lineNumbers: showLineNumbers ? 'on' : 'off',
        minimap: { enabled: showMinimap },
        wordWrap: wordWrap ? 'on' : 'off',
        readOnly: readOnly,
      });
    } catch (error) {
      console.warn('Failed to load Monaco config:', error);
      // Fallback to basic options
      editor.updateOptions({
        fontSize: fontSize,
        lineNumbers: showLineNumbers ? 'on' : 'off',
        minimap: { enabled: showMinimap },
        wordWrap: wordWrap ? 'on' : 'off',
        readOnly: readOnly,
        automaticLayout: true,
        scrollBeyondLastLine: false,
      });
    }

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
      editor.getAction('actions.find').run();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyG, () => {
      editor.getAction('editor.action.goToLine').run();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
      editor.getAction('editor.action.startFindReplaceAction').run();
    });

    // Add additional shortcuts for code navigation
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyD, () => {
      editor.getAction('editor.action.addSelectionToNextFindMatch').run();
    });

    editor.addCommand(monaco.KeyMod.Alt | monaco.KeyCode.UpArrow, () => {
      editor.getAction('editor.action.moveLinesUpAction').run();
    });

    editor.addCommand(monaco.KeyMod.Alt | monaco.KeyCode.DownArrow, () => {
      editor.getAction('editor.action.moveLinesDownAction').run();
    });
  }, [fontSize, showLineNumbers, showMinimap, wordWrap, readOnly, file]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (containerRef.current) {
      if (!isFullscreen) {
        containerRef.current.requestFullscreen?.();
      } else {
        document.exitFullscreen?.();
      }
    }
  };

  const cycleTheme = () => {
    const themes = ['vs-dark', 'vs-light', 'hc-black'] as const;
    const currentIndex = themes.indexOf(currentTheme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setCurrentTheme(nextTheme);
  };

  if (!file) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <File className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">No file selected</p>
          <p className="text-sm">Select a file from the explorer to view its content</p>
        </div>
      </div>
    );
  }

  if (file.type === 'directory') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <File className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">Directory selected</p>
          <p className="text-sm">Select a file to view its content</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className={`flex-1 flex flex-col bg-white dark:bg-gray-900 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}
    >
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3 min-w-0 flex-1">
            <File className="w-4 h-4 text-gray-600 dark:text-gray-400 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h2 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                {file.name}
              </h2>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {file.path}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-1 flex-shrink-0">
            {/* Font size controls */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setFontSize(Math.max(10, fontSize - 2))}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Decrease font size"
              >
                <ZoomOut className="w-3.5 h-3.5" />
              </button>
              <span className="text-xs text-gray-500 dark:text-gray-400 min-w-[2.5rem] text-center">
                {fontSize}px
              </span>
              <button
                onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Increase font size"
              >
                <ZoomIn className="w-3.5 h-3.5" />
              </button>
            </div>

            {/* Theme toggle */}
            <button
              onClick={cycleTheme}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Toggle theme"
            >
              <Settings className="w-3.5 h-3.5" />
            </button>

            {/* Line numbers toggle */}
            <button
              onClick={() => setShowLineNumbers(!showLineNumbers)}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title={showLineNumbers ? "Hide line numbers" : "Show line numbers"}
            >
              {showLineNumbers ? <EyeOff className="w-3.5 h-3.5" /> : <Eye className="w-3.5 h-3.5" />}
            </button>

            {/* Minimap toggle */}
            <button
              onClick={() => setShowMinimap(!showMinimap)}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title={showMinimap ? "Hide minimap" : "Show minimap"}
            >
              <Map className="w-3.5 h-3.5" />
            </button>

            {/* Fullscreen toggle */}
            <button
              onClick={toggleFullscreen}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? <Minimize2 className="w-3.5 h-3.5" /> : <Maximize2 className="w-3.5 h-3.5" />}
            </button>

            {/* Copy button */}
            <button
              onClick={handleCopy}
              disabled={!content?.content}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Copy content"
            >
              {copySuccess ? <Check className="w-3.5 h-3.5 text-green-600" /> : <Copy className="w-3.5 h-3.5" />}
            </button>

            {/* Download button */}
            <button
              onClick={handleDownload}
              disabled={!content?.content}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Download file"
            >
              <Download className="w-3.5 h-3.5" />
            </button>

            {/* Edit button */}
            <button
              onClick={() => onEdit?.(file)}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Edit file"
            >
              <Edit3 className="w-3.5 h-3.5" />
            </button>

            {/* Close button */}
            {onClose && (
              <button
                onClick={onClose}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Close"
              >
                <X className="w-3.5 h-3.5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading file content...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-red-600 dark:text-red-400">
              <AlertCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Failed to load file</p>
              <p className="text-sm">{error}</p>
              <button
                onClick={loadFileContent}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <RotateCcw className="w-4 h-4 inline mr-2" />
                Retry
              </button>
            </div>
          </div>
        ) : content ? (
          <Editor
            height="100%"
            language={getLanguageFromExtension(file.name)}
            value={content.content}
            theme={currentTheme}
            onMount={handleEditorDidMount}
            options={{
              readOnly: readOnly,
              fontSize: fontSize,
              lineNumbers: showLineNumbers ? 'on' : 'off',
              minimap: { enabled: showMinimap },
              wordWrap: wordWrap ? 'on' : 'off',
              automaticLayout: true,
              scrollBeyondLastLine: false,
              renderWhitespace: 'selection',
              renderControlCharacters: true,
              folding: true,
              foldingStrategy: 'indentation',
              showFoldingControls: 'always',
              matchBrackets: 'always',
              bracketPairColorization: { enabled: true },
              guides: {
                bracketPairs: true,
                indentation: true,
              },
              find: {
                addExtraSpaceOnTop: false,
                autoFindInSelection: 'never',
                seedSearchStringFromSelection: 'always',
              },
              contextmenu: true,
              mouseWheelZoom: true,
              smoothScrolling: true,
              cursorBlinking: 'smooth',
              cursorSmoothCaretAnimation: 'on',
            }}
          />
        ) : null}
      </div>

      {/* Footer */}
      {content && (
        <div className="p-2 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400 flex justify-between">
          <span>
            {content.content.split('\n').length} lines, {content.content.length} characters
          </span>
          <span>
            {getLanguageFromExtension(file.name)} • {currentTheme}
          </span>
        </div>
      )}
    </div>
  );
}
