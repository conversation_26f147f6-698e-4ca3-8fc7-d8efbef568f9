'use client';

import React, { useState } from 'react';
import { 
  <PERSON>, 
  Brain, 
  ChevronDown, 
  ChevronRight, 
  <PERSON>ting<PERSON>, 
  Zap,
  Check,
  Copy,
  ExternalLink
} from 'lucide-react';
import { CommandResult } from '@/types/command';

interface CommandResultDisplayProps {
  result: CommandResult;
  onAgentSelect?: (agentName: string) => void;
  onModelSelect?: (providerId: string, modelId: string) => void;
  className?: string;
}

export function CommandResultDisplay({ 
  result, 
  onAgentSelect, 
  onModelSelect,
  className = '' 
}: CommandResultDisplayProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  // If no metadata, just show the regular message parts
  if (!result.metadata) {
    return (
      <div className={`command-result ${className}`}>
        {result.parts?.map((part, index) => (
          <div key={index} className="mb-2">
            {part.type === 'text' && (
              <div className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                {part.text}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  }

  // Handle interactive command results with special UI
  const { metadata } = result;

  if (metadata.type === 'agents' && metadata.agents) {
    return (
      <div className={`command-result ${className}`}>
        {/* Show the regular text response first */}
        {result.parts?.map((part, index) => (
          part.type === 'text' && (
            <div key={index} className="mb-4 text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
              {part.text}
            </div>
          )
        ))}
        
        {/* Enhanced agents UI */}
        <div className="mt-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
          <div 
            className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
            onClick={() => toggleSection('agents')}
          >
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-blue-600" />
              <span className="font-medium">Available Agents ({metadata.agents.length})</span>
            </div>
            {expandedSections.has('agents') ? 
              <ChevronDown className="w-4 h-4" /> : 
              <ChevronRight className="w-4 h-4" />
            }
          </div>
          
          {expandedSections.has('agents') && (
            <div className="border-t p-3 space-y-2">
              {metadata.agents.map((agent: any, index: number) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                  onClick={() => onAgentSelect?.(agent.name)}
                >
                  <div className="flex items-center gap-3">
                    <Brain className="w-4 h-4 text-purple-600" />
                    <div>
                      <div className="font-medium">{agent.name}</div>
                      {agent.description && (
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {agent.description}
                        </div>
                      )}
                      <div className="flex items-center gap-2 mt-1">
                        <span className={`px-2 py-1 text-xs rounded ${
                          agent.mode === 'primary' ? 'bg-blue-100 text-blue-800' :
                          agent.mode === 'subagent' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {agent.mode}
                        </span>
                        {agent.builtIn && (
                          <span className="px-2 py-1 text-xs rounded bg-purple-100 text-purple-800">
                            Built-in
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {agent.model && (
                      <span className="text-xs text-gray-500">
                        {agent.model.providerID}/{agent.model.modelID}
                      </span>
                    )}
                    <ExternalLink className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (metadata.type === 'models' && metadata.providers) {
    return (
      <div className={`command-result ${className}`}>
        {/* Show the regular text response first */}
        {result.parts?.map((part, index) => (
          part.type === 'text' && (
            <div key={index} className="mb-4 text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
              {part.text}
            </div>
          )
        ))}
        
        {/* Enhanced models UI */}
        <div className="mt-4 space-y-3">
          {metadata.providers.providers.map((provider: any, providerIndex: number) => (
            <div key={providerIndex} className="border rounded-lg bg-gray-50 dark:bg-gray-800">
              <div 
                className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                onClick={() => toggleSection(`provider-${provider.id}`)}
              >
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-green-600" />
                  <span className="font-medium">{provider.name}</span>
                  <span className="text-sm text-gray-500">
                    ({Object.keys(provider.models || {}).length} models)
                  </span>
                </div>
                {expandedSections.has(`provider-${provider.id}`) ? 
                  <ChevronDown className="w-4 h-4" /> : 
                  <ChevronRight className="w-4 h-4" />
                }
              </div>
              
              {expandedSections.has(`provider-${provider.id}`) && provider.models && (
                <div className="border-t p-3 space-y-2">
                  {Object.entries(provider.models).map(([modelId, model]: [string, any]) => (
                    <div 
                      key={modelId}
                      className="flex items-center justify-between p-2 border rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                      onClick={() => onModelSelect?.(provider.id, modelId)}
                    >
                      <div>
                        <div className="font-medium">{model.name || modelId}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          Released: {model.release_date || 'Unknown'}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          {model.attachment && (
                            <span className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                              Attachments
                            </span>
                          )}
                          {model.reasoning && (
                            <span className="px-2 py-1 text-xs rounded bg-purple-100 text-purple-800">
                              Reasoning
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {metadata.providers.default[provider.id] === modelId && (
                          <Check className="w-4 h-4 text-green-600" />
                        )}
                        <ExternalLink className="w-4 h-4 text-gray-400" />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Default fallback for other metadata types
  return (
    <div className={`command-result ${className}`}>
      {result.parts?.map((part, index) => (
        <div key={index} className="mb-2">
          {part.type === 'text' && (
            <div className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
              {part.text}
            </div>
          )}
        </div>
      ))}
      
      {metadata && (
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Command metadata available (type: {metadata.type})
          </div>
        </div>
      )}
    </div>
  );
}
