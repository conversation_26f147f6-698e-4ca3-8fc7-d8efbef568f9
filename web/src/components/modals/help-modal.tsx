/**
 * Help Modal
 * Modal for showing help information when /help command is executed
 */

import React from 'react';
import { X, HelpCircle, Keyboard, MessageSquare, Terminal, Settings } from 'lucide-react';

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HelpModal({ isOpen, onClose }: HelpModalProps) {
  if (!isOpen) return null;

  const commandCategories = [
    {
      title: 'UI Commands',
      icon: <Settings className="w-5 h-5" />,
      description: 'Commands that control the interface',
      commands: [
        { name: '/models', description: 'Open model selector' },
        { name: '/agents', description: 'Open agent selector' },
        { name: '/clear', description: 'Clear chat history' },
        { name: '/help', description: 'Show this help dialog' },
        { name: '/sessions', description: 'Open session selector' },
        { name: '/editor', description: 'Open file editor' }
      ]
    },
    {
      title: 'Session Commands',
      icon: <MessageSquare className="w-5 h-5" />,
      description: 'Commands that affect the current session',
      commands: [
        { name: '/init', description: 'Initialize session with agents' },
        { name: '/status', description: 'Show session status' }
      ]
    },
    {
      title: 'Chat Commands',
      icon: <Terminal className="w-5 h-5" />,
      description: 'Commands that send messages to the chat',
      commands: [
        { name: '/search <query>', description: 'Search in files' },
        { name: '/files [pattern]', description: 'List files in workspace' },
        { name: '/history', description: 'Show command history' }
      ]
    }
  ];

  const shortcuts = [
    { key: 'Ctrl + K', description: 'Open command palette' },
    { key: 'Ctrl + Enter', description: 'Send message' },
    { key: 'Shift + Enter', description: 'New line in message' },
    { key: 'Tab', description: 'Complete command suggestion' },
    { key: '↑ / ↓', description: 'Navigate command suggestions' },
    { key: 'Esc', description: 'Close dialogs/suggestions' }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <HelpCircle className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Help & Commands
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="grid gap-8 md:grid-cols-2">
            {/* Commands Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Available Commands
              </h3>
              <div className="space-y-6">
                {commandCategories.map((category, index) => (
                  <div key={index} className="space-y-3">
                    <div className="flex items-center gap-2">
                      <div className="text-blue-500">{category.icon}</div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {category.title}
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 ml-7">
                      {category.description}
                    </p>
                    <div className="ml-7 space-y-2">
                      {category.commands.map((command, cmdIndex) => (
                        <div key={cmdIndex} className="flex items-start gap-3">
                          <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono text-blue-600 dark:text-blue-400 whitespace-nowrap">
                            {command.name}
                          </code>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {command.description}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Shortcuts Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <Keyboard className="w-5 h-5 text-blue-500" />
                Keyboard Shortcuts
              </h3>
              <div className="space-y-3">
                {shortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {shortcut.description}
                    </span>
                    <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded">
                      {shortcut.key}
                    </kbd>
                  </div>
                ))}
              </div>

              {/* Tips Section */}
              <div className="mt-8">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                  Tips
                </h4>
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>Type <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">/</code> to see command suggestions</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>Use <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">@</code> to reference files in your messages</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>Commands starting with <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">/</code> trigger actions, not chat messages</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>Use arrow keys to navigate through command history</span>
                  </div>
                </div>
              </div>

              {/* About Section */}
              <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  About OpenCode
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  OpenCode is an AI-powered development environment that helps you write, 
                  understand, and improve code through natural language interactions.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Need more help? Check the documentation or join our community.
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Got it
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
