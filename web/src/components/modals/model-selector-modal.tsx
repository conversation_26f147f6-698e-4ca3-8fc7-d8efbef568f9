/**
 * Model Selector Modal
 * Modal for selecting models when /models command is executed
 */

import React, { useState, useEffect } from 'react';
import { X, Cpu, Search, Zap } from 'lucide-react';
import { apiService } from '@/lib/api-service';

export interface Provider {
  id: string;
  name: string;
  models: Model[];
}

export interface Model {
  id: string;
  name: string;
  description?: string;
  provider: string;
  capabilities?: string[];
  contextLength?: number;
  pricing?: {
    input?: number;
    output?: number;
  };
}

interface ModelSelectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectModel: (model: Model) => void;
  currentModel?: string;
}

export function ModelSelectorModal({
  isOpen,
  onClose,
  onSelectModel,
  currentModel
}: ModelSelectorModalProps) {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<string>('all');

  useEffect(() => {
    if (isOpen) {
      loadProviders();
    }
  }, [isOpen]);

  const loadProviders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await apiService.getProviders();
      if (result.success && result.data) {
        // Transform the providers data into the expected format
        const transformedProviders: Provider[] = result.data.providers.map(provider => ({
          id: provider.id || provider.name,
          name: provider.name,
          models: provider.models || []
        }));
        setProviders(transformedProviders);
      } else {
        setError(result.error || 'Failed to load models');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load models');
    } finally {
      setLoading(false);
    }
  };

  const allModels = providers.flatMap(provider => 
    provider.models.map(model => ({
      ...model,
      provider: provider.name
    }))
  );

  const filteredModels = allModels.filter(model => {
    const matchesSearch = model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         model.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         model.provider.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesProvider = selectedProvider === 'all' || model.provider === selectedProvider;
    
    return matchesSearch && matchesProvider;
  });

  const handleSelectModel = (model: Model) => {
    onSelectModel(model);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Cpu className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Select Model
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Filters */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search models..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setSelectedProvider('all')}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                selectedProvider === 'all'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              All Providers
            </button>
            {providers.map(provider => (
              <button
                key={provider.id}
                onClick={() => setSelectedProvider(provider.name)}
                className={`px-3 py-1 rounded-full text-sm transition-colors ${
                  selectedProvider === provider.name
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {provider.name} ({provider.models.length})
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-96">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading models...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-red-500 mb-2">Failed to load models</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">{error}</div>
              <button
                onClick={loadProviders}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Retry
              </button>
            </div>
          ) : filteredModels.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              {searchQuery || selectedProvider !== 'all' ? 'No models found matching your criteria' : 'No models available'}
            </div>
          ) : (
            <div className="grid gap-3 md:grid-cols-2">
              {filteredModels.map((model) => (
                <div
                  key={`${model.provider}-${model.id}`}
                  onClick={() => handleSelectModel(model)}
                  className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                    currentModel === model.id
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {model.name}
                        </h3>
                        {currentModel === model.id && (
                          <span className="px-2 py-1 text-xs bg-blue-500 text-white rounded-full">
                            Current
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {model.provider}
                      </div>
                      {model.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                          {model.description}
                        </p>
                      )}
                      {model.contextLength && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                          Context: {model.contextLength.toLocaleString()} tokens
                        </div>
                      )}
                      {model.capabilities && model.capabilities.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {model.capabilities.slice(0, 2).map((capability, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded"
                            >
                              {capability}
                            </span>
                          ))}
                          {model.capabilities.length > 2 && (
                            <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">
                              +{model.capabilities.length - 2}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    <Zap className="w-4 h-4 text-gray-400 ml-2" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex justify-end gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
