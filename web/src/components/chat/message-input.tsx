'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Loader2, Square, Command as CommandIcon, File, Folder } from 'lucide-react';
import { CommandSuggestion } from '@/types/command';
import { apiService } from '@/lib/api-service';

interface FileSuggestion {
  name: string;
  path: string;
  type: 'file' | 'directory';
  isDirectory: boolean;
}

interface MessageInputProps {
  onSendMessage: (message: string) => Promise<void>;
  onExecuteCommand?: (command: string, args: string) => Promise<void>;
  isLoading?: boolean;
  onStop?: () => void;
  disabled?: boolean;
  placeholder?: string;
  sessionId?: string;
}

export function MessageInput({
  onSendMessage,
  onExecuteCommand,
  isLoading = false,
  onStop,
  disabled = false,
  placeholder = "Type your message...",
  sessionId
}: MessageInputProps) {
  const [message, setMessage] = useState('');
  const [showCommandSuggestions, setShowCommandSuggestions] = useState(false);
  const [commandSuggestions, setCommandSuggestions] = useState<CommandSuggestion[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [isCommandMode, setIsCommandMode] = useState(false);
  const [isFileMode, setIsFileMode] = useState(false);
  const [showFileSuggestions, setShowFileSuggestions] = useState(false);
  const [fileSuggestions, setFileSuggestions] = useState<FileSuggestion[]>([]);
  const [selectedFileSuggestion, setSelectedFileSuggestion] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  }, [message]);

  // Built-in commands that are always available
  const builtInCommands: CommandSuggestion[] = [
    { name: 'help', description: 'Show available commands and help', template: '/help' },
    { name: 'clear', description: 'Clear the chat history', template: '/clear' },
    { name: 'status', description: 'Show session status', template: '/status' },
    { name: 'agents', description: 'List available agents', template: '/agents' },
    { name: 'models', description: 'List available models', template: '/models' },
    { name: 'files', description: 'List files in workspace', template: '/files' },
    { name: 'search', description: 'Search in files', template: '/search' },
    { name: 'init', description: 'Initialize session with agents', template: '/init' },
    { name: 'history', description: 'Show command history', template: '/history' },
    { name: 'sessions', description: 'List all sessions', template: '/sessions' }
  ];

  // Check if message starts with / for command mode or @ for file mode
  useEffect(() => {
    const isCommand = message.startsWith('/');
    const isFile = message.includes('@') && !isCommand;

    setIsCommandMode(isCommand);
    setIsFileMode(isFile);

    if (isCommand) {
      const query = message.substring(1);
      loadCommandSuggestions(query);
      setShowFileSuggestions(false);
      setFileSuggestions([]);
    } else if (isFile) {
      // Find the last @ symbol and extract the query after it
      const lastAtIndex = message.lastIndexOf('@');
      const query = message.substring(lastAtIndex + 1);
      loadFileSuggestions(query);
      setShowCommandSuggestions(false);
      setCommandSuggestions([]);
    } else {
      setShowCommandSuggestions(false);
      setCommandSuggestions([]);
      setSelectedSuggestion(0);
      setShowFileSuggestions(false);
      setFileSuggestions([]);
      setSelectedFileSuggestion(0);
    }
  }, [message]);

  // Load command suggestions
  const loadCommandSuggestions = async (query: string) => {
    try {
      // Always start with built-in commands
      let allCommands = [...builtInCommands];

      // Try to get custom commands from API
      try {
        const response = await apiService.getCommands();
        if (response.success && response.data && response.data.length > 0) {
          const customCommands = response.data.map(cmd => ({
            name: cmd.name,
            description: cmd.description,
            template: `/${cmd.name}`,
            agent: cmd.agent,
            model: cmd.model
          }));
          allCommands = [...customCommands, ...builtInCommands];
        }
      } catch (error) {
        console.warn('Failed to load custom commands, using built-in only:', error);
      }

      // Filter commands based on query
      const filtered = allCommands
        .filter(cmd => {
          if (!query) return true; // Show all commands when just "/" is typed
          const lowerQuery = query.toLowerCase();
          return cmd.name.toLowerCase().startsWith(lowerQuery) ||
                 cmd.name.toLowerCase().includes(lowerQuery) ||
                 cmd.description?.toLowerCase().includes(lowerQuery);
        })
        .sort((a, b) => {
          // Prioritize exact matches and prefix matches
          const aStartsWith = a.name.toLowerCase().startsWith(query.toLowerCase());
          const bStartsWith = b.name.toLowerCase().startsWith(query.toLowerCase());
          if (aStartsWith && !bStartsWith) return -1;
          if (!aStartsWith && bStartsWith) return 1;
          return a.name.localeCompare(b.name);
        })
        .slice(0, 8); // Show more suggestions

      setCommandSuggestions(filtered);
      setShowCommandSuggestions(filtered.length > 0);
      setSelectedSuggestion(0);
    } catch (error) {
      console.error('Failed to load command suggestions:', error);
      // Fallback to built-in commands only
      const filtered = builtInCommands
        .filter(cmd => {
          if (!query) return true;
          const lowerQuery = query.toLowerCase();
          return cmd.name.toLowerCase().startsWith(lowerQuery) ||
                 cmd.name.toLowerCase().includes(lowerQuery);
        })
        .slice(0, 8);

      setCommandSuggestions(filtered);
      setShowCommandSuggestions(filtered.length > 0);
      setSelectedSuggestion(0);
    }
  };

  // Load file suggestions
  const loadFileSuggestions = async (query: string) => {
    try {
      let allFiles: FileSuggestion[] = [];

      // Try to get files from API
      try {
        const files = await apiService.searchFiles(query || '');
        allFiles = files.map(filePath => {
          const fileName = filePath.split('/').pop() || filePath;
          const isDirectory = filePath.endsWith('/');
          return {
            name: fileName,
            path: filePath,
            type: isDirectory ? 'directory' : 'file',
            isDirectory
          };
        });
      } catch (error) {
        console.warn('Failed to load files from API, using fallback:', error);
        // Fallback to common file patterns
        const commonFiles = [
          'README.md', 'package.json', 'tsconfig.json', 'src/', 'components/',
          'pages/', 'app/', 'lib/', 'utils/', 'types/', 'styles/', 'public/'
        ];
        allFiles = commonFiles
          .filter(file => !query || file.toLowerCase().includes(query.toLowerCase()))
          .map(file => ({
            name: file,
            path: file,
            type: file.endsWith('/') ? 'directory' : 'file',
            isDirectory: file.endsWith('/')
          }));
      }

      // Filter files based on query
      const filtered = allFiles
        .filter(file => {
          if (!query) return true; // Show all files when just "@" is typed
          const lowerQuery = query.toLowerCase();
          return file.name.toLowerCase().includes(lowerQuery) ||
                 file.path.toLowerCase().includes(lowerQuery);
        })
        .sort((a, b) => {
          // Prioritize exact matches and prefix matches
          const aStartsWith = a.name.toLowerCase().startsWith(query.toLowerCase());
          const bStartsWith = b.name.toLowerCase().startsWith(query.toLowerCase());
          if (aStartsWith && !bStartsWith) return -1;
          if (!aStartsWith && bStartsWith) return 1;
          // Prioritize files over directories
          if (!a.isDirectory && b.isDirectory) return -1;
          if (a.isDirectory && !b.isDirectory) return 1;
          return a.name.localeCompare(b.name);
        })
        .slice(0, 8); // Show more suggestions

      setFileSuggestions(filtered);
      setShowFileSuggestions(filtered.length > 0);
      setSelectedFileSuggestion(0);
    } catch (error) {
      console.error('Failed to load file suggestions:', error);
      setFileSuggestions([]);
      setShowFileSuggestions(false);
      setSelectedFileSuggestion(0);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const trimmedMessage = message.trim();
    if (!trimmedMessage || isLoading || disabled) return;

    const messageToSend = trimmedMessage;
    setMessage('');
    setShowCommandSuggestions(false);

    try {
      // Check if it's a command
      if (messageToSend.startsWith('/') && onExecuteCommand) {
        const [command, ...args] = messageToSend.substring(1).split(' ');
        await onExecuteCommand(command, args.join(' '));
      } else {
        await onSendMessage(messageToSend);
      }
    } catch (error) {
      // Restore message on error
      setMessage(messageToSend);
      console.error('Failed to send message:', error);
    }
  };

  // Scroll selected suggestion into view
  useEffect(() => {
    if ((showCommandSuggestions || showFileSuggestions) && suggestionsRef.current) {
      const selectedIndex = showCommandSuggestions ? selectedSuggestion : selectedFileSuggestion;
      const selectedElement = suggestionsRef.current.children[selectedIndex + 1]; // +1 for header
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedSuggestion, selectedFileSuggestion, showCommandSuggestions, showFileSuggestions]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle command suggestions
    if (showCommandSuggestions && commandSuggestions.length > 0) {
      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          setSelectedSuggestion(prev => {
            const newIndex = prev <= 0 ? commandSuggestions.length - 1 : prev - 1;
            return newIndex;
          });
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedSuggestion(prev => {
            const newIndex = prev >= commandSuggestions.length - 1 ? 0 : prev + 1;
            return newIndex;
          });
          break;
        case 'Tab':
          e.preventDefault();
          if (commandSuggestions[selectedSuggestion]) {
            const suggestion = commandSuggestions[selectedSuggestion];
            setMessage(suggestion.template + ' ');
            setShowCommandSuggestions(false);
            textareaRef.current?.focus();
          }
          break;
        case 'Enter':
          if (!e.shiftKey) {
            e.preventDefault();
            if (commandSuggestions[selectedSuggestion]) {
              const suggestion = commandSuggestions[selectedSuggestion];
              setMessage(suggestion.template + ' ');
              setShowCommandSuggestions(false);
              textareaRef.current?.focus();
            }
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowCommandSuggestions(false);
          setSelectedSuggestion(0);
          break;
      }
    }
    // Handle file suggestions
    else if (showFileSuggestions && fileSuggestions.length > 0) {
      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          setSelectedFileSuggestion(prev => {
            const newIndex = prev <= 0 ? fileSuggestions.length - 1 : prev - 1;
            return newIndex;
          });
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedFileSuggestion(prev => {
            const newIndex = prev >= fileSuggestions.length - 1 ? 0 : prev + 1;
            return newIndex;
          });
          break;
        case 'Tab':
          e.preventDefault();
          if (fileSuggestions[selectedFileSuggestion]) {
            const suggestion = fileSuggestions[selectedFileSuggestion];
            const lastAtIndex = message.lastIndexOf('@');
            const beforeAt = message.substring(0, lastAtIndex + 1);
            setMessage(beforeAt + suggestion.path + ' ');
            setShowFileSuggestions(false);
            textareaRef.current?.focus();
          }
          break;
        case 'Enter':
          if (!e.shiftKey) {
            e.preventDefault();
            if (fileSuggestions[selectedFileSuggestion]) {
              const suggestion = fileSuggestions[selectedFileSuggestion];
              const lastAtIndex = message.lastIndexOf('@');
              const beforeAt = message.substring(0, lastAtIndex + 1);
              setMessage(beforeAt + suggestion.path + ' ');
              setShowFileSuggestions(false);
              textareaRef.current?.focus();
            }
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowFileSuggestions(false);
          setSelectedFileSuggestion(0);
          break;
      }
    }
    // Handle special cases
    else if (e.key === 'Tab' && isCommandMode && message === '/') {
      // Show all commands when user presses Tab after typing just "/"
      e.preventDefault();
      loadCommandSuggestions('');
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };



  const handleStop = () => {
    if (onStop) {
      onStop();
    }
  };

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex gap-3 items-end">
          {/* Message input */}
          <div className="flex-1 relative">
            <div className="relative">
              {(isCommandMode || isFileMode) && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
                  {isCommandMode ? (
                    <CommandIcon className="w-4 h-4 text-blue-500" />
                  ) : (
                    <File className="w-4 h-4 text-green-500" />
                  )}
                </div>
              )}
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={disabled ? "Select a session to start chatting..." :
                  isCommandMode ? "Type command name or press Tab to see all commands..." :
                  isFileMode ? "Type file name to reference..." :
                  "Type your message... Use / for commands or @ to reference files"}
                disabled={disabled || isLoading}
                className={`w-full resize-none rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 ${
                  (isCommandMode || isFileMode) ? 'pl-10' : 'pl-4'
                } py-3 pr-12 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed ${
                  isCommandMode ? 'border-blue-300 dark:border-blue-600' :
                  isFileMode ? 'border-green-300 dark:border-green-600' : ''
                }`}
                rows={1}
                style={{ minHeight: '48px', maxHeight: '200px' }}
              />
            </div>

            {/* Command Suggestions */}
            {showCommandSuggestions && commandSuggestions.length > 0 && (
              <div
                ref={suggestionsRef}
                className="absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-xl z-50 max-h-64 overflow-y-auto backdrop-blur-sm animate-in slide-in-from-bottom-2 duration-200"
              >
                <div className="p-3 text-xs font-semibold text-gray-600 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-t-lg">
                  <div className="flex items-center gap-2">
                    <CommandIcon className="w-3 h-3" />
                    Available Commands ({commandSuggestions.length})
                  </div>
                </div>
                {commandSuggestions.map((suggestion, index) => (
                  <div
                    key={`${suggestion.name}-${index}`}
                    className={`px-4 py-3 cursor-pointer transition-all duration-150 border-l-2 ${
                      index === selectedSuggestion
                        ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-l-blue-500 shadow-sm'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border-l-transparent'
                    }`}
                    onClick={() => {
                      setMessage(suggestion.template + ' ');
                      setShowCommandSuggestions(false);
                      textareaRef.current?.focus();
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-sm font-semibold text-gray-900 dark:text-white">
                            /{suggestion.name}
                          </span>
                          {index === selectedSuggestion && (
                            <span className="text-xs px-1.5 py-0.5 bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300 rounded-full font-medium">
                              ↵ Enter
                            </span>
                          )}
                        </div>
                        {suggestion.description && (
                          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 leading-relaxed">
                            {suggestion.description}
                          </div>
                        )}
                      </div>
                      {(suggestion.agent || suggestion.model) && (
                        <div className="flex items-center space-x-1 ml-3 flex-shrink-0">
                          {suggestion.agent && (
                            <span className="text-xs px-2 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-md font-medium">
                              {suggestion.agent}
                            </span>
                          )}
                          {suggestion.model && (
                            <span className="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 rounded-md font-medium">
                              {suggestion.model}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                <div className="p-2 text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-b-lg">
                  <div className="flex items-center justify-between">
                    <span>Use ↑↓ to navigate, Tab or Enter to select</span>
                    <span>Esc to close</span>
                  </div>
                </div>
              </div>
            )}

            {/* File Suggestions */}
            {showFileSuggestions && fileSuggestions.length > 0 && (
              <div
                ref={suggestionsRef}
                className="absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-xl z-50 max-h-64 overflow-y-auto backdrop-blur-sm animate-in slide-in-from-bottom-2 duration-200"
              >
                <div className="p-3 text-xs font-semibold text-gray-600 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-t-lg">
                  <div className="flex items-center gap-2">
                    <File className="w-3 h-3" />
                    Available Files ({fileSuggestions.length})
                  </div>
                </div>
                {fileSuggestions.map((suggestion, index) => (
                  <div
                    key={`${suggestion.path}-${index}`}
                    className={`px-4 py-3 cursor-pointer transition-all duration-150 border-l-2 ${
                      index === selectedFileSuggestion
                        ? 'bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-l-green-500 shadow-sm'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border-l-transparent'
                    }`}
                    onClick={() => {
                      const lastAtIndex = message.lastIndexOf('@');
                      const beforeAt = message.substring(0, lastAtIndex + 1);
                      setMessage(beforeAt + suggestion.path + ' ');
                      setShowFileSuggestions(false);
                      textareaRef.current?.focus();
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <div className="flex-shrink-0">
                            {suggestion.isDirectory ? (
                              <Folder className="w-4 h-4 text-blue-500" />
                            ) : (
                              <File className="w-4 h-4 text-gray-500" />
                            )}
                          </div>
                          <span className="font-mono text-sm font-semibold text-gray-900 dark:text-white truncate">
                            {suggestion.name}
                          </span>
                          {index === selectedFileSuggestion && (
                            <span className="text-xs px-1.5 py-0.5 bg-green-100 dark:bg-green-800 text-green-600 dark:text-green-300 rounded-full font-medium">
                              ↵ Enter
                            </span>
                          )}
                        </div>
                        {suggestion.path !== suggestion.name && (
                          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 leading-relaxed font-mono">
                            {suggestion.path}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-1 ml-3 flex-shrink-0">
                        <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-md font-medium">
                          {suggestion.type}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
                <div className="p-2 text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-b-lg">
                  <div className="flex items-center justify-between">
                    <span>Use ↑↓ to navigate, Tab or Enter to select</span>
                    <span>Esc to close</span>
                  </div>
                </div>
              </div>
            )}

            {/* Character count */}
            {message.length > 0 && (
              <div className="absolute bottom-1 right-1 text-xs text-gray-400">
                {message.length}
              </div>
            )}
          </div>

          {/* Send/Stop button */}
          <div className="flex gap-2">
            {isLoading ? (
              <button
                type="button"
                onClick={handleStop}
                className="flex items-center justify-center w-12 h-12 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                aria-label="Stop generation"
              >
                <Square className="w-4 h-4" />
              </button>
            ) : (
              <button
                type="submit"
                disabled={!message.trim() || disabled}
                className="flex items-center justify-center w-12 h-12 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                aria-label="Send message"
              >
                <Send className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Helper text */}
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center justify-between">
          <div>
            {isCommandMode ? (
              <span>
                Type <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">/</kbd> for commands •
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs ml-1">↑↓</kbd> navigate •
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs ml-1">Tab</kbd> complete
              </span>
            ) : (
              <span>
                Press <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">Enter</kbd> to send,
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs ml-1">Shift + Enter</kbd> for new line
              </span>
            )}
          </div>
          {isCommandMode && (
            <div className="text-blue-600 dark:text-blue-400">
              Command Mode
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
